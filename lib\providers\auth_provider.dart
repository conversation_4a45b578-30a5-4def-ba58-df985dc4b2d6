import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_model.dart';
import '../services/email_verification_service.dart';
import '../services/firebase_email_link_service.dart';
import '../services/admin_service.dart';
import '../utils/logger.dart';

/// مزود المصادقة المتكامل مع Firebase
class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId:
        '801031214670-qhqjqhqjqhqjqhqjqhqjqhqjqhqjqhqj.apps.googleusercontent.com',
  );

  // حالة المصادقة
  User? _firebaseUser;
  UserModel? _userModel;
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;
  String? _successMessage;

  // Getters
  User? get firebaseUser => _firebaseUser;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  String? get successMessage => _successMessage;
  bool get isAuthenticated => _firebaseUser != null;
  bool get isEmailVerified => _firebaseUser?.emailVerified ?? false;

  /// تهيئة مزود المصادقة
  AuthProvider() {
    _initializeAuth();
  }

  /// تهيئة المصادقة والاستماع لتغييرات حالة المستخدم
  void _initializeAuth() async {
    try {
      // التحقق من المستخدم الحالي
      _firebaseUser = _auth.currentUser;

      if (_firebaseUser != null) {
        await _loadUserModel();
      }

      _isInitialized = true;
      notifyListeners();

      // الاستماع لتغييرات حالة المصادقة
      _auth.authStateChanges().listen((User? user) async {
        _firebaseUser = user;

        if (user != null) {
          await _loadUserModel();
        } else {
          _userModel = null;
        }

        notifyListeners();
      });
    } catch (e) {
      _setError('خطأ في تهيئة المصادقة: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// تحميل نموذج المستخدم من Firestore
  Future<void> _loadUserModel() async {
    if (_firebaseUser == null) return;

    try {
      final doc =
          await _firestore.collection('users').doc(_firebaseUser!.uid).get();

      if (doc.exists) {
        _userModel = UserModel.fromFirestore(doc);
      } else {
        // إنشاء نموذج مستخدم جديد إذا لم يكن موجوداً
        await _createUserModel();
      }
    } catch (e) {
      // تجاهل أخطاء تحميل نموذج المستخدم
    }
  }

  /// إنشاء نموذج مستخدم جديد في Firestore
  Future<void> _createUserModel() async {
    if (_firebaseUser == null) return;

    try {
      final userModel = UserModel(
        id: _firebaseUser!.uid,
        email: _firebaseUser!.email ?? '',
        displayName: _firebaseUser!.displayName ?? 'مستخدم',
        photoURL: _firebaseUser!.photoURL,
        academicYear: 'السنة الأولى',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: _firebaseUser!.emailVerified,
        loginProvider: 'email',
      );

      await _firestore
          .collection('users')
          .doc(_firebaseUser!.uid)
          .set(userModel.toMap());

      _userModel = userModel;
    } catch (e) {
      // تجاهل أخطاء إنشاء نموذج المستخدم
    }
  }

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<bool> signInWithEmail(String email, String password) async {
    return await _performAuth(() async {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        await _updateLastLogin();
        return true;
      }
      return false;
    }, 'تسجيل الدخول');
  }

  /// إنشاء حساب جديد مع التحقق من البريد الإلكتروني
  Future<bool> createAccount({
    required String email,
    required String password,
    required String displayName,
    required String verificationCode,
  }) async {
    return await _performAuth(() async {
      // التحقق من كود التحقق أولاً
      final verificationResult = await EmailVerificationService.verifyCode(
        email.trim(),
        verificationCode.trim(),
      );

      if (!verificationResult.success) {
        _setError(verificationResult.message);
        return false;
      }

      // إنشاء الحساب
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // تحديث اسم المستخدم
        await credential.user!.updateDisplayName(displayName.trim());
        await credential.user!.reload();
        _firebaseUser = _auth.currentUser;

        // إنشاء نموذج المستخدم في Firestore
        await _createUserModel();

        // حذف كود التحقق المستخدم
        await EmailVerificationService.deleteVerificationCode(email.trim());

        _setSuccess('تم إنشاء الحساب بنجاح!');
        return true;
      }
      return false;
    }, 'إنشاء الحساب');
  }

  /// إرسال كود التحقق للبريد الإلكتروني
  Future<String?> sendVerificationCode(String email) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من وجود الحساب مسبقاً
      try {
        await _auth.createUserWithEmailAndPassword(
          email: email.trim(),
          password: 'temp_password_for_check',
        );
        // إذا نجح إنشاء المستخدم، فهذا يعني أن البريد غير مسجل
        // نحذف المستخدم المؤقت
        await _auth.currentUser?.delete();
      } catch (e) {
        if (e.toString().contains('email-already-in-use')) {
          _setError('هذا البريد الإلكتروني مسجل بالفعل. يرجى تسجيل الدخول.');
          return null;
        }
        // إذا كان خطأ آخر، نتجاهله ونكمل
      }

      // إرسال كود التحقق
      final code = await EmailVerificationService.sendVerificationCode(
        email.trim(),
      );

      if (code != null) {
        // عرض الكود في رسالة النجاح للاختبار
        _setSuccess(
          'تم إرسال كود التحقق إلى بريدك الإلكتروني\nكود التحقق للاختبار: $code',
        );
        return code;
      } else {
        _setError('فشل في إرسال كود التحقق. يرجى المحاولة مرة أخرى.');
        return null;
      }
    } catch (e) {
      _setError('خطأ في إرسال كود التحقق: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال رابط التحقق للبريد الإلكتروني (الطريقة الجديدة)
  Future<String?> sendVerificationLink({
    required String email,
    required String password,
    required String displayName,
  }) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من وجود الحساب مسبقاً
      try {
        await _auth.createUserWithEmailAndPassword(
          email: email.trim(),
          password: 'temp_password_for_check',
        );
        // إذا نجح إنشاء المستخدم، فهذا يعني أن البريد غير مسجل
        // نحذف المستخدم المؤقت
        await _auth.currentUser?.delete();
      } catch (e) {
        if (e.toString().contains('email-already-in-use')) {
          _setError('هذا البريد الإلكتروني مسجل بالفعل. يرجى تسجيل الدخول.');
          return null;
        }
        // إذا كان خطأ آخر، نتجاهله ونكمل
      }

      // إرسال رابط التحقق الحقيقي عبر Firebase
      final verificationLink =
          await FirebaseEmailLinkService.sendVerificationLink(
            email.trim(),
            password,
            displayName,
          );

      if (verificationLink != null) {
        _setSuccess(
          'تم إرسال رابط التحقق إلى بريدك الإلكتروني. يرجى فتح الرابط لتفعيل حسابك.',
        );
        return verificationLink;
      } else {
        _setError('فشل في إرسال رابط التحقق. يرجى المحاولة مرة أخرى.');
        return null;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء إرسال رابط التحقق: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الدخول بـ Google
  Future<bool> signInWithGoogle() async {
    return await _performAuth(() async {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return false;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _updateLastLogin();
        return true;
      }
      return false;
    }, 'تسجيل الدخول بـ Google');
  }

  /// تسجيل الدخول كضيف
  Future<bool> signInAnonymously() async {
    return await _performAuth(() async {
      final credential = await _auth.signInAnonymously();

      if (credential.user != null) {
        await _createUserModel();
        return true;
      }
      return false;
    }, 'تسجيل الدخول كضيف');
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _auth.signOut();
      await _googleSignIn.signOut();
      _userModel = null;
      _setSuccess('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال رابط إعادة ضبط كلمة المرور
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من صحة البريد الإلكتروني
      if (email.trim().isEmpty) {
        _setError('يرجى إدخال البريد الإلكتروني');
        return false;
      }

      if (!_isValidEmail(email.trim())) {
        _setError('يرجى إدخال بريد إلكتروني صالح');
        return false;
      }

      // إرسال رابط إعادة ضبط كلمة المرور
      final success = await FirebaseEmailLinkService.sendPasswordResetEmail(
        email.trim(),
      );

      if (success) {
        _setSuccess(
          'تم إرسال رابط إعادة ضبط كلمة المرور إلى بريدك الإلكتروني. يرجى فتح الرابط لإعادة ضبط كلمة المرور.',
        );
        return true;
      } else {
        _setError('فشل في إرسال رابط إعادة ضبط كلمة المرور');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إرسال رابط إعادة ضبط كلمة المرور: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث آخر تسجيل دخول
  Future<void> _updateLastLogin() async {
    if (_firebaseUser == null) return;

    try {
      await _firestore.collection('users').doc(_firebaseUser!.uid).update({
        'lastLoginAt': Timestamp.fromDate(DateTime.now()),
      });

      // التحقق من صلاحيات الأدمن وتحديث AdminProvider
      if (_firebaseUser!.email != null) {
        final isAdminUser = await AdminService.isAdmin(_firebaseUser!.email!);
        if (isAdminUser) {
          AppLogger.auth('تم تسجيل دخول أدمن: ${_firebaseUser!.email}');
          // تحديث AdminProvider
          // سيتم التحقق من الأدمن في AdminDashboardScreen
        }
      }
    } catch (e) {
      // تجاهل أخطاء تحديث آخر تسجيل دخول
    }
  }

  /// تنفيذ عملية مصادقة مع معالجة الأخطاء
  Future<bool> _performAuth(
    Future<bool> Function() authFunction,
    String operation,
  ) async {
    try {
      _setLoading(true);
      _clearMessages();

      final result = await authFunction();

      if (result) {
        _setSuccess('تم $operation بنجاح!');
      }

      return result;
    } on FirebaseAuthException catch (e) {
      _handleFirebaseAuthError(e);
      return false;
    } catch (e) {
      _setError('خطأ في $operation: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// معالجة أخطاء Firebase Auth
  void _handleFirebaseAuthError(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
        break;
      case 'wrong-password':
        _setError('كلمة المرور غير صحيحة');
        break;
      case 'email-already-in-use':
        _setError('هذا البريد الإلكتروني مستخدم بالفعل');
        break;
      case 'weak-password':
        _setError('كلمة المرور ضعيفة. يجب أن تكون 6 أحرف على الأقل');
        break;
      case 'invalid-email':
        _setError('البريد الإلكتروني غير صحيح');
        break;
      case 'too-many-requests':
        _setError('تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً');
        break;
      default:
        _setError('حدث خطأ: ${e.message}');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة خطأ
  void _setError(String error) {
    _error = error;
    _successMessage = null;
    notifyListeners();
  }

  /// تعيين رسالة نجاح
  void _setSuccess(String message) {
    _successMessage = message;
    _error = null;
    notifyListeners();
  }

  /// مسح الرسائل
  void _clearMessages() {
    _error = null;
    _successMessage = null;
    notifyListeners();
  }

  /// مسح الرسائل يدوياً
  void clearMessages() {
    _clearMessages();
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من صلاحيات الأدمن
  Future<bool> isAdmin() async {
    if (_firebaseUser?.email == null) return false;
    return await AdminService.isAdmin(_firebaseUser!.email!);
  }
}
