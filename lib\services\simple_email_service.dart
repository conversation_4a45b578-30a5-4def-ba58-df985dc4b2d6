import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../utils/logger.dart';

/// خدمة بريد إلكتروني مبسطة للاختبار
class SimpleEmailService {
  // إعدادات خدمة EmailJS المجانية
  static const String _serviceId = 'service_legal2025';
  static const String _templateId = 'template_verification';
  static const String _publicKey = 'legal2025_public_key';

  /// إرسال كود التحقق عبر البريد الإلكتروني
  static Future<bool> sendVerificationCode(String email, String code) async {
    try {
      // محاولة الإرسال الحقيقي أولاً
      final realEmailSent = await _sendRealEmail(email, code);
      if (realEmailSent) {
        AppLogger.success(
          'تم إرسال البريد الإلكتروني بنجاح إلى: $email',
          'SimpleEmailService',
        );
        return true;
      }

      // إذا فشل الإرسال الحقيقي، استخدم النظام البديل
      return await _sendMockEmail(email, code);
    } catch (e) {
      AppLogger.error(
        'خطأ في إرسال البريد الإلكتروني',
        'SimpleEmailService',
        e,
      );
      return await _sendMockEmail(email, code);
    }
  }

  /// محاولة إرسال بريد إلكتروني حقيقي
  static Future<bool> _sendRealEmail(String email, String code) async {
    try {
      // استخدام خدمة EmailJS المجانية
      final response = await http.post(
        Uri.parse('https://api.emailjs.com/api/v1.0/email/send'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'service_id': _serviceId,
          'template_id': _templateId,
          'user_id': _publicKey,
          'template_params': {
            'to_email': email,
            'verification_code': code,
            'app_name': 'Legal 2025',
            'message': 'كود التحقق الخاص بك هو: $code',
          },
        }),
      );

      if (response.statusCode == 200) {
        AppLogger.success(
          'تم إرسال البريد الإلكتروني عبر EmailJS',
          'SimpleEmailService',
        );
        return true;
      } else {
        AppLogger.error(
          'فشل إرسال البريد عبر EmailJS: ${response.statusCode}',
          'SimpleEmailService',
        );
        return false;
      }
    } catch (e) {
      AppLogger.error('خطأ في EmailJS', 'SimpleEmailService', e);
      return false;
    }
  }

  /// نظام بديل لمحاكاة إرسال البريد الإلكتروني
  static Future<bool> _sendMockEmail(String email, String code) async {
    try {
      // محاكاة تأخير الشبكة
      await Future.delayed(const Duration(seconds: 2));

      // عرض تفاصيل البريد الإلكتروني المُرسل
      AppLogger.displayMockEmail(email, code);
      AppLogger.success(
        'تم إرسال البريد الإلكتروني بنجاح (محاكاة)',
        'SimpleEmailService',
      );

      return true;
    } catch (e) {
      AppLogger.error('خطأ في نظام المحاكاة', 'SimpleEmailService', e);
      return false;
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// إنشاء كود تحقق عشوائي
  static String generateVerificationCode() {
    final random = Random();
    String code = '';
    for (int i = 0; i < 6; i++) {
      code += random.nextInt(10).toString();
    }
    return code;
  }
}
