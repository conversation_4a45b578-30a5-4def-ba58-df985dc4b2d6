import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart'
    hide NotificationSettings;
import '../models/notification_model.dart';
import '../utils/logger.dart';

/// خدمة الإشعارات المحسنة
class EnhancedNotificationService {
  static final EnhancedNotificationService _instance =
      EnhancedNotificationService._internal();
  factory EnhancedNotificationService() => _instance;
  EnhancedNotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Collections
  static const String notificationsCollection = 'notifications';
  static const String userSettingsCollection = 'user_notification_settings';

  // Stream controllers
  final StreamController<List<NotificationModel>> _notificationsController =
      StreamController<List<NotificationModel>>.broadcast();
  final StreamController<int> _unreadCountController =
      StreamController<int>.broadcast();

  // State
  NotificationSettings _settings = NotificationSettings();
  List<NotificationModel> _notifications = [];
  StreamSubscription<QuerySnapshot>? _notificationsSubscription;

  // Getters
  Stream<List<NotificationModel>> get notificationsStream =>
      _notificationsController.stream;
  Stream<int> get unreadCountStream => _unreadCountController.stream;
  List<NotificationModel> get notifications => _notifications;
  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  NotificationSettings get settings => _settings;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    try {
      await _initializeLocalNotifications();
      await _initializeFirebaseMessaging();
      await _loadUserSettings();
      await _startListeningToNotifications();

      AppLogger.success(
        'تم تهيئة خدمة الإشعارات المحسنة',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error(
        'خطأ في تهيئة خدمة الإشعارات',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // طلب الأذونات
    await _requestPermissions();
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeFirebaseMessaging() async {
    // طلب الأذونات
    await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    // الحصول على FCM token
    final token = await _messaging.getToken();
    if (token != null) {
      await _saveFCMToken(token);
    }

    // الاستماع لتحديث التوكن
    _messaging.onTokenRefresh.listen(_saveFCMToken);

    // معالجة الرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالجة الرسائل عند فتح التطبيق
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.requestNotificationsPermission();
  }

  /// حفظ FCM token
  Future<void> _saveFCMToken(String token) async {
    final user = _auth.currentUser;
    if (user != null) {
      await _firestore.collection('users').doc(user.uid).update({
        'fcmToken': token,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });
    }
  }

  /// تحميل إعدادات المستخدم
  Future<void> _loadUserSettings() async {
    final user = _auth.currentUser;
    if (user == null) return;

    try {
      final doc =
          await _firestore
              .collection(userSettingsCollection)
              .doc(user.uid)
              .get();

      if (doc.exists) {
        _settings = NotificationSettings.fromMap(doc.data()!);
      }
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل إعدادات الإشعارات',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// بدء الاستماع للإشعارات
  Future<void> _startListeningToNotifications() async {
    final user = _auth.currentUser;
    if (user == null) return;

    _notificationsSubscription?.cancel();
    _notificationsSubscription = _firestore
        .collection(notificationsCollection)
        .where('userId', isEqualTo: user.uid)
        .orderBy('createdAt', descending: true)
        .limit(100)
        .snapshots()
        .listen(
          _onNotificationsChanged,
          onError: (error) {
            AppLogger.error(
              'خطأ في الاستماع للإشعارات',
              'EnhancedNotificationService',
              error,
            );
          },
        );
  }

  /// معالجة تغيير الإشعارات
  void _onNotificationsChanged(QuerySnapshot snapshot) {
    _notifications =
        snapshot.docs
            .map((doc) => NotificationModel.fromFirestore(doc))
            .where((notification) => !notification.isExpired)
            .toList();

    _notificationsController.add(_notifications);
    _unreadCountController.add(unreadCount);
  }

  /// إرسال إشعار جديد
  Future<void> sendNotification(NotificationModel notification) async {
    try {
      // التحقق من إعدادات المستخدم
      if (!_settings.isTypeEnabled(notification.type)) {
        return;
      }

      // حفظ في Firestore
      final docRef = await _firestore
          .collection(notificationsCollection)
          .add(notification.toFirestore());

      // إرسال إشعار محلي إذا كان المستخدم الحالي هو المستلم
      final currentUser = _auth.currentUser;
      if (currentUser?.uid == notification.userId) {
        await _showLocalNotification(notification.copyWith(id: docRef.id));
      }

      AppLogger.info(
        'تم إرسال إشعار: ${notification.title}',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error('خطأ في إرسال الإشعار', 'EnhancedNotificationService', e);
    }
  }

  /// إظهار إشعار محلي
  Future<void> _showLocalNotification(NotificationModel notification) async {
    if (_settings.isQuietTime) return;

    const androidDetails = AndroidNotificationDetails(
      'legal_app_channel',
      'Legal App Notifications',
      channelDescription: 'إشعارات تطبيق القانون والشريعة',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.hashCode,
      notification.title,
      notification.body,
      details,
      payload: notification.id,
    );
  }

  /// معالجة الرسائل في المقدمة
  void _handleForegroundMessage(RemoteMessage message) {
    // يتم التعامل مع الإشعارات من خلال Firestore listener
  }

  /// معالجة الرسائل عند فتح التطبيق
  void _handleMessageOpenedApp(RemoteMessage message) {
    // التنقل إلى الصفحة المناسبة حسب نوع الإشعار
    _navigateToNotificationTarget(message.data);
  }

  /// معالجة النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      final notification = _notifications.firstWhere(
        (n) => n.id == response.payload,
        orElse: () => throw Exception('Notification not found'),
      );

      // وضع علامة مقروء
      markAsRead(notification.id);

      // التنقل إلى الهدف
      _navigateToNotificationTarget(notification.data);
    }
  }

  /// التنقل إلى هدف الإشعار
  void _navigateToNotificationTarget(Map<String, dynamic> data) {
    // سيتم تنفيذ التنقل حسب نوع الإشعار
    // يمكن استخدام Navigator أو Router للتنقل
  }

  /// وضع علامة مقروء على إشعار
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});

      AppLogger.info(
        'تم وضع علامة مقروء على الإشعار',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error(
        'خطأ في وضع علامة مقروء',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// وضع علامة مقروء على جميع الإشعارات
  Future<void> markAllAsRead() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final batch = _firestore.batch();
      final unreadNotifications = _notifications.where((n) => !n.isRead);

      for (final notification in unreadNotifications) {
        final docRef = _firestore
            .collection(notificationsCollection)
            .doc(notification.id);
        batch.update(docRef, {'isRead': true});
      }

      await batch.commit();
      AppLogger.info(
        'تم وضع علامة مقروء على جميع الإشعارات',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error(
        'خطأ في وضع علامة مقروء على جميع الإشعارات',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .delete();

      AppLogger.info('تم حذف الإشعار', 'EnhancedNotificationService');
    } catch (e) {
      AppLogger.error('خطأ في حذف الإشعار', 'EnhancedNotificationService', e);
    }
  }

  /// حذف جميع الإشعارات المقروءة
  Future<void> deleteReadNotifications() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final batch = _firestore.batch();
      final readNotifications = _notifications.where((n) => n.isRead);

      for (final notification in readNotifications) {
        final docRef = _firestore
            .collection(notificationsCollection)
            .doc(notification.id);
        batch.delete(docRef);
      }

      await batch.commit();
      AppLogger.info(
        'تم حذف جميع الإشعارات المقروءة',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error(
        'خطأ في حذف الإشعارات المقروءة',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateSettings(NotificationSettings newSettings) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await _firestore
          .collection(userSettingsCollection)
          .doc(user.uid)
          .set(newSettings.toMap());

      _settings = newSettings;
      AppLogger.info(
        'تم تحديث إعدادات الإشعارات',
        'EnhancedNotificationService',
      );
    } catch (e) {
      AppLogger.error(
        'خطأ في تحديث إعدادات الإشعارات',
        'EnhancedNotificationService',
        e,
      );
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _notificationsSubscription?.cancel();
    _notificationsController.close();
    _unreadCountController.close();
    AppLogger.info('تم تنظيف خدمة الإشعارات', 'EnhancedNotificationService');
  }

  /// إرسال إشعارات للتفاعلات الاجتماعية
  static Future<void> sendLikeNotification({
    required String postAuthorId,
    required String likerName,
    required String postId,
    required String postTitle,
  }) async {
    if (postAuthorId == FirebaseAuth.instance.currentUser?.uid) return;

    final notification = NotificationModel.like(
      userId: postAuthorId,
      likerName: likerName,
      postId: postId,
      postTitle: postTitle,
    );

    await EnhancedNotificationService().sendNotification(notification);
  }

  static Future<void> sendCommentNotification({
    required String postAuthorId,
    required String commenterName,
    required String postId,
    required String postTitle,
    required String commentText,
  }) async {
    if (postAuthorId == FirebaseAuth.instance.currentUser?.uid) return;

    final notification = NotificationModel.comment(
      userId: postAuthorId,
      commenterName: commenterName,
      postId: postId,
      postTitle: postTitle,
      commentText: commentText,
    );

    await EnhancedNotificationService().sendNotification(notification);
  }

  static Future<void> sendShareNotification({
    required String postAuthorId,
    required String sharerName,
    required String postId,
    required String postTitle,
  }) async {
    if (postAuthorId == FirebaseAuth.instance.currentUser?.uid) return;

    final notification = NotificationModel.share(
      userId: postAuthorId,
      sharerName: sharerName,
      postId: postId,
      postTitle: postTitle,
    );

    await EnhancedNotificationService().sendNotification(notification);
  }
}
