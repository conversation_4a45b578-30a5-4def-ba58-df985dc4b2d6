import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import '../config/email_config.dart';
import 'real_email_service.dart';
import '../utils/logger.dart';

/// نتيجة التحقق من كود البريد الإلكتروني
class VerificationResult {
  final bool success;
  final String message;
  final String? errorCode;

  const VerificationResult({
    required this.success,
    required this.message,
    this.errorCode,
  });

  factory VerificationResult.success([String? message]) {
    return VerificationResult(
      success: true,
      message: message ?? 'تم التحقق بنجاح',
    );
  }

  factory VerificationResult.failure(String message, [String? errorCode]) {
    return VerificationResult(
      success: false,
      message: message,
      errorCode: errorCode,
    );
  }
}

/// خدمة التحقق من البريد الإلكتروني باستخدام كود مكون من 6 أرقام
class EmailVerificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'email_verifications';

  // مدة انتهاء صلاحية الكود (10 دقائق)
  static const Duration _codeExpiration = Duration(minutes: 10);

  // الحد الأقصى لعدد المحاولات
  static const int _maxAttempts = 5;

  /// إنشاء كود تحقق مكون من 6 أرقام
  static String generateVerificationCode() {
    final random = Random();
    String code = '';
    for (int i = 0; i < 6; i++) {
      code += random.nextInt(10).toString();
    }
    return code;
  }

  /// تشفير كود التحقق لحفظه بشكل آمن
  static String _hashCode(String code) {
    final bytes = utf8.encode(code);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// حفظ كود التحقق في Firestore
  static Future<bool> saveVerificationCode(String email, String code) async {
    try {
      final hashedCode = _hashCode(code);
      final expiresAt = DateTime.now().add(_codeExpiration);

      await _firestore.collection(_collectionName).doc(email).set({
        'hashedCode': hashedCode,
        'expiresAt': Timestamp.fromDate(expiresAt),
        'attempts': 0,
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'isUsed': false,
      });

      return true;
    } catch (e) {
      return false;
    }
  }

  /// التحقق من صحة كود التحقق
  static Future<VerificationResult> verifyCode(
    String email,
    String code,
  ) async {
    try {
      final docRef = _firestore.collection(_collectionName).doc(email);
      final doc = await docRef.get();

      if (!doc.exists) {
        return VerificationResult.failure(
          'لم يتم العثور على كود التحقق. يرجى طلب كود جديد.',
          'CODE_NOT_FOUND',
        );
      }

      final data = doc.data()!;
      final hashedCode = data['hashedCode'] as String;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final attempts = data['attempts'] as int;
      final isUsed = data['isUsed'] as bool? ?? false;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().isAfter(expiresAt)) {
        await docRef.delete(); // حذف الكود المنتهي الصلاحية
        return VerificationResult.failure(
          'انتهت صلاحية كود التحقق. يرجى طلب كود جديد.',
          'CODE_EXPIRED',
        );
      }

      // التحقق من استخدام الكود مسبقاً
      if (isUsed) {
        return VerificationResult.failure(
          'تم استخدام هذا الكود مسبقاً. يرجى طلب كود جديد.',
          'CODE_ALREADY_USED',
        );
      }

      // التحقق من عدد المحاولات
      if (attempts >= _maxAttempts) {
        await docRef.delete(); // حذف الكود بعد تجاوز الحد الأقصى
        return VerificationResult.failure(
          'تم تجاوز الحد الأقصى للمحاولات. يرجى طلب كود جديد.',
          'MAX_ATTEMPTS_EXCEEDED',
        );
      }

      // التحقق من صحة الكود
      final inputHashedCode = _hashCode(code);
      if (inputHashedCode != hashedCode) {
        // زيادة عدد المحاولات
        await docRef.update({'attempts': attempts + 1});

        final remainingAttempts = _maxAttempts - attempts - 1;
        return VerificationResult.failure(
          'كود التحقق غير صحيح. المحاولات المتبقية: $remainingAttempts',
          'INVALID_CODE',
        );
      }

      // الكود صحيح - تحديث حالة الاستخدام
      await docRef.update({'isUsed': true});

      return VerificationResult.success('تم التحقق من البريد الإلكتروني بنجاح');
    } catch (e) {
      return VerificationResult.failure(
        'حدث خطأ أثناء التحقق من الكود. يرجى المحاولة مرة أخرى.',
        'VERIFICATION_ERROR',
      );
    }
  }

  /// إرسال كود التحقق عبر البريد الإلكتروني
  static Future<String?> sendVerificationCode(String email) async {
    try {
      final code = generateVerificationCode();

      // حفظ الكود في Firestore
      final saved = await saveVerificationCode(email, code);
      if (!saved) {
        return null;
      }

      // محاولة إرسال البريد الإلكتروني
      AppLogger.info('جاري إرسال كود التحقق...', 'EmailVerification');
      final emailSent = await _sendEmailWithCode(email, code);

      // إذا فشل الإرسال التقليدي، استخدم النظام البديل
      if (!emailSent) {
        AppLogger.info('جاري المحاولة بطريقة بديلة...', 'EmailVerification');
        await RealEmailService.sendVerificationCode(email, code);
      }

      // عرض الكود بوضوح للاختبار
      _displayVerificationCode(email, code);

      return code; // إرجاع الكود للاختبار
    } catch (e) {
      AppLogger.error('خطأ في إرسال كود التحقق', 'EmailVerification', e);
      return null;
    }
  }

  /// عرض كود التحقق بوضوح
  static void _displayVerificationCode(String email, String code) {
    AppLogger.displayVerificationCode(email, code);
  }

  /// إرسال البريد الإلكتروني مع كود التحقق
  static Future<bool> _sendEmailWithCode(String email, String code) async {
    try {
      // التحقق من تكوين البريد الإلكتروني
      if (!EmailConfig.isConfigured) {
        AppLogger.warning(
          'إعدادات البريد الإلكتروني غير مُكونة',
          'EmailVerification',
        );
        AppLogger.email('كود التحقق للاختبار: $code');
        return false;
      }

      // إعداد خادم SMTP مع إعدادات محسنة
      final smtpServer = SmtpServer(
        'smtp.gmail.com',
        port: 587,
        username: EmailConfig.senderEmail,
        password: EmailConfig.appPassword,
        allowInsecure: false,
        ssl: false,
      );

      // إنشاء الرسالة
      final message =
          Message()
            ..from = Address(EmailConfig.senderEmail, EmailConfig.senderName)
            ..recipients.add(email)
            ..subject = 'كود التحقق - ${EmailConfig.senderName}'
            ..html = _buildEmailTemplate(code);

      // إرسال البريد الإلكتروني مع معالجة أفضل للأخطاء
      final sendReport = await send(message, smtpServer);

      AppLogger.success(
        'تم إرسال البريد الإلكتروني بنجاح إلى: $email',
        'EmailVerification',
      );
      AppLogger.email('تقرير الإرسال: ${sendReport.toString()}');

      return true;
    } catch (e) {
      AppLogger.error('فشل في إرسال البريد الإلكتروني', 'EmailVerification', e);
      AppLogger.email('كود التحقق للاختبار: $code');

      // محاولة إرسال بديلة مع إعدادات مختلفة
      return await _sendEmailAlternative(email, code);
    }
  }

  /// طريقة بديلة لإرسال البريد الإلكتروني
  static Future<bool> _sendEmailAlternative(String email, String code) async {
    try {
      // استخدام إعدادات Gmail البديلة
      final smtpServer = gmail(
        EmailConfig.senderEmail,
        EmailConfig.appPassword,
      );

      final message =
          Message()
            ..from = Address(EmailConfig.senderEmail, EmailConfig.senderName)
            ..recipients.add(email)
            ..subject = 'كود التحقق - ${EmailConfig.senderName}'
            ..text = '''
مرحباً!

كود التحقق الخاص بك هو: $code

هذا الكود صالح لمدة 10 دقائق فقط.

تطبيق Legal 2025
        ''';

      await send(message, smtpServer);
      AppLogger.success(
        'تم إرسال البريد الإلكتروني بالطريقة البديلة',
        'EmailVerification',
      );
      return true;
    } catch (e) {
      AppLogger.error('فشل في الطريقة البديلة أيضاً', 'EmailVerification', e);
      return false;
    }
  }

  /// إنشاء قالب البريد الإلكتروني
  static String _buildEmailTemplate(String code) {
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>كود التحقق</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                direction: rtl;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 40px 20px;
                text-align: center;
                color: white;
            }
            .header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: bold;
            }
            .content {
                padding: 40px 30px;
                text-align: center;
            }
            .code-container {
                background: #f8fafc;
                border: 2px dashed #6366f1;
                border-radius: 15px;
                padding: 30px;
                margin: 30px 0;
            }
            .code {
                font-size: 36px;
                font-weight: bold;
                color: #6366f1;
                letter-spacing: 8px;
                margin: 10px 0;
            }
            .message {
                font-size: 16px;
                color: #374151;
                line-height: 1.6;
                margin: 20px 0;
            }
            .warning {
                background: #fef3cd;
                border: 1px solid #fbbf24;
                border-radius: 10px;
                padding: 15px;
                margin: 20px 0;
                color: #92400e;
                font-size: 14px;
            }
            .footer {
                background: #f9fafb;
                padding: 20px;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 كود التحقق</h1>
                <p>تطبيق Legal 2025</p>
            </div>

            <div class="content">
                <h2>مرحباً بك!</h2>
                <p class="message">
                    لإكمال عملية إنشاء حسابك في تطبيق Legal 2025، يرجى استخدام كود التحقق التالي:
                </p>

                <div class="code-container">
                    <div class="code">$code</div>
                    <p>أدخل هذا الكود في التطبيق</p>
                </div>

                <div class="warning">
                    ⚠️ هذا الكود صالح لمدة 10 دقائق فقط ولا يمكن استخدامه أكثر من مرة واحدة.
                </div>

                <p class="message">
                    إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.
                </p>
            </div>

            <div class="footer">
                <p>© 2025 Legal 2025. جميع الحقوق محفوظة.</p>
                <p>هذه رسالة تلقائية، يرجى عدم الرد عليها.</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  /// حذف كود التحقق (للتنظيف)
  static Future<void> deleteVerificationCode(String email) async {
    try {
      await _firestore.collection(_collectionName).doc(email).delete();
    } catch (e) {
      // تجاهل أخطاء الحذف
    }
  }

  /// التحقق من وجود كود صالح للبريد الإلكتروني
  static Future<bool> hasValidCode(String email) async {
    try {
      final doc = await _firestore.collection(_collectionName).doc(email).get();

      if (!doc.exists) return false;

      final data = doc.data()!;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final isUsed = data['isUsed'] as bool? ?? false;

      return !isUsed && DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }

  /// تنظيف الأكواد المنتهية الصلاحية (يمكن استدعاؤها دورياً)
  static Future<void> cleanupExpiredCodes() async {
    try {
      final now = Timestamp.fromDate(DateTime.now());
      final expiredCodes =
          await _firestore
              .collection(_collectionName)
              .where('expiresAt', isLessThan: now)
              .get();

      final batch = _firestore.batch();
      for (final doc in expiredCodes.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      // تجاهل أخطاء التنظيف
    }
  }
}
