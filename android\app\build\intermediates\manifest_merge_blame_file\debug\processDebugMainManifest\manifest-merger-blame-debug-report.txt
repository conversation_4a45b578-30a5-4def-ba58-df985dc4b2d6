1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.legal2025.yamy"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:5-79
16-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:5-68
17-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:22-65
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:5-66
18-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:22-63
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:5-81
19-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:8:5-77
20-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:8:22-74
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:71:5-76:15
29        <intent>
29-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:72:9-75:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:73:13-72
30-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:73:21-70
31
32            <data android:mimeType="text/plain" />
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:74:13-50
32-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:74:19-48
33        </intent>
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:10:5-65:19
43        android:name="android.app.Application"
43-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:12:9-47
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="false"
47        android:icon="@mipmap/ic_launcher"
47-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:13:9-43
48        android:label="تطبيق الشريعة والقانون" >
48-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:11:9-47
49        <activity
49-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:14:9-35:20
50            android:name="com.legal2025.yamy.MainActivity"
50-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:15:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:20:13-163
52            android:exported="true"
52-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:16:13-36
53            android:hardwareAccelerated="true"
53-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:21:13-47
54            android:launchMode="singleTop"
54-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:17:13-43
55            android:taskAffinity=""
55-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:18:13-36
56            android:theme="@style/LaunchTheme"
56-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:19:13-47
57            android:windowSoftInputMode="adjustResize" >
57-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:22:13-55
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
65-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:27:13-30:17
66                android:name="io.flutter.embedding.android.NormalTheme"
66-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:28:15-70
67                android:resource="@style/NormalTheme" />
67-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:29:15-52
68
69            <intent-filter>
69-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:31:13-34:29
70                <action android:name="android.intent.action.MAIN" />
70-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:32:17-68
70-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:32:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:33:17-76
72-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:33:27-74
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
79-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:38:9-40:33
80            android:name="flutterEmbedding"
80-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:39:13-44
81            android:value="2" />
81-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:40:13-30
82
83        <!-- Firebase Cloud Messaging -->
84        <service
84-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:43:9-49:19
85            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
85-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:44:13-97
86            android:exported="false" >
86-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:45:13-37
87            <intent-filter>
87-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:46:13-48:29
88                <action android:name="com.google.firebase.MESSAGING_EVENT" />
88-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:47:17-78
88-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:47:25-75
89            </intent-filter>
90        </service>
91
92        <!-- Firebase default notification channel -->
93        <meta-data
93-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:52:9-54:55
94            android:name="com.google.firebase.messaging.default_notification_channel_id"
94-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-89
95            android:value="high_importance_channel" />
95-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:54:13-52
96
97        <!-- Firebase default notification icon -->
98        <meta-data
98-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:57:9-59:68
99            android:name="com.google.firebase.messaging.default_notification_icon"
99-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:58:13-83
100            android:resource="@drawable/ic_stat_ic_notification" />
100-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:59:13-65
101
102        <!-- Firebase default notification color -->
103        <meta-data
103-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:62:9-64:53
104            android:name="com.google.firebase.messaging.default_notification_color"
104-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:63:13-84
105            android:resource="@color/colorAccent" />
105-->D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:64:13-50
106
107        <provider
107-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
108            android:name="androidx.startup.InitializationProvider"
108-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
109            android:authorities="com.legal2025.yamy.androidx-startup"
109-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
110            android:exported="false" >
110-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
111            <meta-data
111-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.emoji2.text.EmojiCompatInitializer"
112-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
113                android:value="androidx.startup" />
113-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
114            <meta-data
114-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
115                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
115-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
116                android:value="androidx.startup" />
116-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
117        </provider>
118    </application>
119
120</manifest>
