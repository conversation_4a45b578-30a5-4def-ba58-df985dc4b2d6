import 'package:cloud_firestore/cloud_firestore.dart';

class CommunityPost {
  final String id;
  final String authorId;
  final String authorName;
  final String content;
  final DateTime timestamp;
  final List<String> imageUrls;
  final Map<String, dynamic>? poll;
  final List<String> likedBy;
  final List<String> sharedBy;
  final String? userRole;
  final bool isPinned;
  final List<String> tags;
  final String category;

  CommunityPost({
    required this.id,
    required this.authorId,
    required this.authorName,
    required this.content,
    required this.timestamp,
    this.imageUrls = const [],
    this.poll,
    this.likedBy = const [],
    this.sharedBy = const [],
    this.userRole,
    this.isPinned = false,
    this.tags = const [],
    this.category = 'عام',
  });

  // Convert to Map for Firebase
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'authorId': authorId,
      'authorName': authorName,
      'content': content,
      'timestamp': Timestamp.fromDate(timestamp),
      'imageUrls': imageUrls,
      'poll': poll,
      'likedBy': likedBy,
      'sharedBy': sharedBy,
      'userRole': userRole,
      'isPinned': isPinned,
      'tags': tags,
      'category': category,
    };
  }

  // Create from Firebase document
  factory CommunityPost.fromMap(Map<String, dynamic> map, String documentId) {
    return CommunityPost(
      id: documentId,
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? 'مستخدم مجهول',
      content: map['content'] ?? '',
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      poll: map['poll'],
      likedBy: List<String>.from(map['likedBy'] ?? []),
      sharedBy: List<String>.from(map['sharedBy'] ?? []),
      userRole: map['userRole'],
      isPinned: map['isPinned'] ?? false,
      tags: List<String>.from(map['tags'] ?? []),
      category: map['category'] ?? 'عام',
    );
  }

  // Helper getters
  int get likesCount => likedBy.length;
  int get sharesCount => sharedBy.length;
  
  bool isLikedBy(String userId) => likedBy.contains(userId);
  bool isSharedBy(String userId) => sharedBy.contains(userId);

  // Copy with method for updates
  CommunityPost copyWith({
    String? id,
    String? authorId,
    String? authorName,
    String? content,
    DateTime? timestamp,
    List<String>? imageUrls,
    Map<String, dynamic>? poll,
    List<String>? likedBy,
    List<String>? sharedBy,
    String? userRole,
    bool? isPinned,
    List<String>? tags,
    String? category,
  }) {
    return CommunityPost(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      imageUrls: imageUrls ?? this.imageUrls,
      poll: poll ?? this.poll,
      likedBy: likedBy ?? this.likedBy,
      sharedBy: sharedBy ?? this.sharedBy,
      userRole: userRole ?? this.userRole,
      isPinned: isPinned ?? this.isPinned,
      tags: tags ?? this.tags,
      category: category ?? this.category,
    );
  }
}

class CommunityComment {
  final String id;
  final String postId;
  final String authorId;
  final String authorName;
  final String content;
  final DateTime timestamp;
  final List<String> likedBy;

  CommunityComment({
    required this.id,
    required this.postId,
    required this.authorId,
    required this.authorName,
    required this.content,
    required this.timestamp,
    this.likedBy = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'authorId': authorId,
      'authorName': authorName,
      'content': content,
      'timestamp': Timestamp.fromDate(timestamp),
      'likedBy': likedBy,
    };
  }

  factory CommunityComment.fromMap(Map<String, dynamic> map, String documentId) {
    return CommunityComment(
      id: documentId,
      postId: map['postId'] ?? '',
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? 'مستخدم مجهول',
      content: map['content'] ?? '',
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      likedBy: List<String>.from(map['likedBy'] ?? []),
    );
  }

  int get likesCount => likedBy.length;
  bool isLikedBy(String userId) => likedBy.contains(userId);
}

class CommunityStats {
  final int totalUsers;
  final int activeUsers;
  final int totalPosts;
  final int todayPosts;
  final int totalComments;
  final int todayComments;
  final DateTime lastUpdated;

  CommunityStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.totalPosts,
    required this.todayPosts,
    required this.totalComments,
    required this.todayComments,
    required this.lastUpdated,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalUsers': totalUsers,
      'activeUsers': activeUsers,
      'totalPosts': totalPosts,
      'todayPosts': todayPosts,
      'totalComments': totalComments,
      'todayComments': todayComments,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
    };
  }

  factory CommunityStats.fromMap(Map<String, dynamic> map) {
    return CommunityStats(
      totalUsers: map['totalUsers'] ?? 0,
      activeUsers: map['activeUsers'] ?? 0,
      totalPosts: map['totalPosts'] ?? 0,
      todayPosts: map['todayPosts'] ?? 0,
      totalComments: map['totalComments'] ?? 0,
      todayComments: map['todayComments'] ?? 0,
      lastUpdated: (map['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}
