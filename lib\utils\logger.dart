import 'package:flutter/foundation.dart';

/// نظام تسجيل محسن للتطبيق
class AppLogger {
  static const String _tag = 'Legal2025';

  /// تسجيل رسالة معلومات
  static void info(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('ℹ️ ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة نجاح
  static void success(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('✅ ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة تحذير
  static void warning(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('⚠️ ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة خطأ
  static void error(String message, [String? tag, Object? error]) {
    if (kDebugMode) {
      debugPrint('❌ ${tag ?? _tag}: $message');
      if (error != null) {
        debugPrint('   التفاصيل: $error');
      }
    }
  }

  /// تسجيل رسالة تطوير
  static void debug(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('🔧 ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة شبكة
  static void network(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('🌐 ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة قاعدة بيانات
  static void database(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('🗄️ ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة مصادقة
  static void auth(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('🔐 ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة محادثة
  static void chat(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('💬 ${tag ?? _tag}: $message');
    }
  }

  /// تسجيل رسالة بريد إلكتروني
  static void email(String message, [String? tag]) {
    if (kDebugMode) {
      debugPrint('📧 ${tag ?? _tag}: $message');
    }
  }

  /// عرض كود التحقق بتنسيق جميل
  static void displayVerificationCode(String email, String code) {
    if (!kDebugMode) return;

    debugPrint('');
    debugPrint('🎯 ═══════════════════════════════════════════════════════════');
    debugPrint('🎯                    كود التحقق جاهز!                        ');
    debugPrint('🎯 ═══════════════════════════════════════════════════════════');
    debugPrint('🎯');
    debugPrint('🎯 البريد الإلكتروني: $email');
    debugPrint('🎯');
    debugPrint('🎯 ┌─────────────────────────────────────────────────────────┐');
    debugPrint('🎯 │                                                         │');
    debugPrint('🎯 │                  كود التحقق الخاص بك:                  │');
    debugPrint('🎯 │                                                         │');
    debugPrint('🎯 │                        $code                        │');
    debugPrint('🎯 │                                                         │');
    debugPrint('🎯 │              صالح لمدة 10 دقائق فقط                   │');
    debugPrint('🎯 │                                                         │');
    debugPrint('🎯 └─────────────────────────────────────────────────────────┘');
    debugPrint('🎯');
    debugPrint('🎯 يرجى إدخال هذا الكود في التطبيق لإكمال عملية التسجيل');
    debugPrint('🎯');
    debugPrint('🎯 ═══════════════════════════════════════════════════════════');
    debugPrint('');
  }

  /// عرض رابط التحقق بتنسيق جميل
  static void displayVerificationLink(String email, String link) {
    if (!kDebugMode) return;

    debugPrint('');
    debugPrint('🔗 ═══════════════════════════════════════════════════════════');
    debugPrint('🔗                    رابط التحقق جاهز!                        ');
    debugPrint('🔗 ═══════════════════════════════════════════════════════════');
    debugPrint('🔗');
    debugPrint('🔗 البريد الإلكتروني: $email');
    debugPrint('🔗');
    debugPrint('🔗 ┌─────────────────────────────────────────────────────────┐');
    debugPrint('🔗 │                                                         │');
    debugPrint('🔗 │                  رابط التحقق الخاص بك:                  │');
    debugPrint('🔗 │                                                         │');
    debugPrint('🔗 │ $link');
    debugPrint('🔗 │                                                         │');
    debugPrint('🔗 │              صالح لمدة 30 دقيقة فقط                   │');
    debugPrint('🔗 │                                                         │');
    debugPrint('🔗 └─────────────────────────────────────────────────────────┘');
    debugPrint('🔗');
    debugPrint('🔗 يرجى النقر على الرابط أو نسخه في المتصفح لتفعيل حسابك');
    debugPrint('🔗');
    debugPrint('🔗 ملاحظة: هذا الرابط يظهر هنا لأن إرسال البريد الإلكتروني');
    debugPrint('🔗         لم ينجح. في الإنتاج، سيتم إرسال البريد فقط.');
    debugPrint('🔗');
    debugPrint('🔗 ═══════════════════════════════════════════════════════════');
    debugPrint('');
  }

  /// عرض نتائج الاختبار
  static void displayTestResults(Map<String, bool> results) {
    if (!kDebugMode) return;

    debugPrint('\n=== نتائج اختبار نظام المحادثة ===');
    results.forEach((test, passed) {
      final status = passed ? '✅ نجح' : '❌ فشل';
      debugPrint('$test: $status');
    });
    
    final passedCount = results.values.where((v) => v).length;
    final totalCount = results.length;
    debugPrint('\nالنتيجة الإجمالية: $passedCount/$totalCount اختبارات نجحت');
    
    if (passedCount == totalCount) {
      debugPrint('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
    } else {
      debugPrint('⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
    }
  }

  /// عرض محتوى بريد إلكتروني محاكي
  static void displayMockEmail(String email, String code) {
    if (!kDebugMode) return;

    debugPrint('📧 ═══════════════════════════════════════');
    debugPrint('📧 محاكاة إرسال بريد إلكتروني');
    debugPrint('📧 ═══════════════════════════════════════');
    debugPrint('📧 إلى: $email');
    debugPrint('📧 الموضوع: كود التحقق - تطبيق Legal 2025');
    debugPrint('📧 ═══════════════════════════════════════');
    debugPrint('📧 محتوى الرسالة:');
    debugPrint('📧');
    debugPrint('📧 مرحباً!');
    debugPrint('📧');
    debugPrint('📧 كود التحقق الخاص بك هو:');
    debugPrint('📧');
    debugPrint('📧 ┌─────────────┐');
    debugPrint('📧 │    $code    │');
    debugPrint('📧 └─────────────┘');
    debugPrint('📧');
    debugPrint('📧 هذا الكود صالح لمدة 10 دقائق فقط.');
    debugPrint('📧');
    debugPrint('📧 تطبيق Legal 2025');
    debugPrint('📧 ═══════════════════════════════════════');
  }
}
