# نظام المحادثة الفورية - Firebase Realtime Database

## نظرة عامة

تم تطوير نظام محادثة فورية شامل باستخدام Firebase Realtime Database يوفر:

- **غرف محادثة للسنوات الدراسية**: غرفة منفصلة لكل سنة دراسية
- **غرفة محادثة عامة**: مثبتة في الأعلى بتصميم مميز
- **انضمام صريح**: المستخدمون يجب أن ينضموا يدوياً للغرف
- **مؤشرات الحضور**: عرض المستخدمين المتصلين
- **مؤشرات الكتابة**: إظهار من يكتب حالياً
- **رسائل فورية**: تحديث فوري للرسائل
- **تخزين آخر 150 رسالة**: لكل غرفة محادثة

## الملفات المضافة/المحدثة

### ملفات جديدة
```
lib/services/chat_service.dart          - خدمة المحادثة الفورية
lib/screens/chat_room_screen.dart       - شاشة المحادثة الداخلية
lib/test_chat_system.dart              - اختبارات النظام
database.rules.json                    - قواعد أمان قاعدة البيانات
CHAT_SYSTEM_README.md                  - هذا الملف
```

### ملفات محدثة
```
lib/models/chat_model.dart              - نماذج البيانات مع دعم Realtime DB
lib/screens/chat_screen.dart            - شاشة عرض غرف المحادثة
lib/main.dart                          - تهيئة النظام وحالة الحضور
pubspec.yaml                           - التبعيات (firebase_database موجود)
```

## هيكل قاعدة البيانات

```
legal2025-default-rtdb/
├── chatRooms/
│   ├── general/
│   │   ├── name: "المحادثة العامة"
│   │   ├── description: "محادثة عامة لجميع الطلاب"
│   │   ├── isGeneral: true
│   │   ├── members/
│   │   ├── lastMessage: "آخر رسالة"
│   │   └── lastMessageTime: timestamp
│   ├── year_1/
│   ├── year_2/
│   ├── year_3/
│   └── year_4/
├── chats/
│   ├── general/
│   │   └── messages/
│   │       └── messageId/
│   │           ├── senderId: "uid"
│   │           ├── senderName: "اسم المرسل"
│   │           ├── message: "نص الرسالة"
│   │           ├── timestamp: timestamp
│   │           └── type: "text"
│   └── year_1/
├── presence/
│   └── userId/
│       ├── name: "اسم المستخدم"
│       ├── isOnline: true/false
│       └── lastSeen: timestamp
└── typing/
    └── chatRoomId/
        └── userId/
            ├── userName: "اسم المستخدم"
            └── timestamp: timestamp
```

## الميزات المنفذة

### ✅ إدارة غرف المحادثة
- إنشاء غرف افتراضية (عامة + 4 سنوات دراسية)
- تدفق فوري لقائمة الغرف
- تصميم مميز للغرفة العامة (مثبتة في الأعلى)

### ✅ نظام الانضمام والخروج
- انضمام صريح للغرف (ما عدا العامة)
- إمكانية مغادرة الغرف
- التحقق من العضوية قبل الوصول

### ✅ المراسلة الفورية
- إرسال واستقبال فوري للرسائل
- تخزين آخر 150 رسالة لكل غرفة
- عرض اسم المرسل والوقت
- تصميم فقاعات محادثة حديث

### ✅ مؤشرات الحضور
- تحديث حالة الاتصال تلقائياً
- عرض المستخدمين المتصلين
- إزالة الحضور عند قطع الاتصال

### ✅ مؤشرات الكتابة
- إظهار من يكتب حالياً
- إزالة تلقائية بعد 5 ثوان
- تنظيف دوري للمؤشرات المنتهية

### ✅ الأمان
- قواعد أمان شاملة
- التحقق من الهوية
- حماية البيانات الشخصية

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
flutter run
```

### 2. الوصول للمحادثات
- انتقل لتبويب "الدردشة" في الملاحة السفلية
- ستظهر قائمة بغرف المحادثة المتاحة

### 3. الانضمام لغرفة
- اضغط على أي غرفة سنة دراسية
- اضغط "انضمام" للدخول
- الغرفة العامة متاحة مباشرة

### 4. المحادثة
- اكتب رسالتك في الحقل السفلي
- اضغط إرسال أو Enter
- ستظهر الرسائل فورياً

### 5. مغادرة الغرفة
- اضغط أيقونة الخروج في شريط التطبيق
- أكد المغادرة

## اختبار النظام

### اختبار سريع
```dart
import 'lib/test_chat_system.dart';

// تشغيل اختبارات سريعة
final results = await ChatSystemTester.runQuickTests();
ChatSystemTester.printTestResults(results);
```

### اختبار شامل
- افتح `ChatSystemTestScreen` من داخل التطبيق
- اضغط "تشغيل جميع الاختبارات"
- راجع النتائج

## قواعد الأمان

### قراءة البيانات
- المحادثات: للأعضاء والغرف العامة فقط
- الحضور: لجميع المستخدمين المسجلين
- مؤشرات الكتابة: للأعضاء فقط

### كتابة البيانات
- الرسائل: للأعضاء فقط مع التحقق من الهوية
- العضوية: المستخدم يمكنه إضافة/إزالة نفسه فقط
- الحضور: المستخدم يحدث حالته فقط

## الأداء والتحسينات

### تحسينات مطبقة
- تحديد آخر 150 رسالة لكل غرفة
- تنظيف دوري لمؤشرات الكتابة المنتهية
- استخدام StreamBuilder للتحديثات الفورية
- إدارة ذاكرة محسنة مع dispose

### مراقبة الأداء
- مؤقتات للتنظيف التلقائي
- إلغاء الاشتراكات عند الخروج
- تحديث الحضور عند دخول/خروج التطبيق

## استكشاف الأخطاء

### مشاكل شائعة
1. **لا تظهر الرسائل**: تحقق من قواعد الأمان
2. **لا يعمل الانضمام**: تحقق من حالة المصادقة
3. **مؤشرات الكتابة لا تختفي**: سيتم تنظيفها تلقائياً

### سجلات التشخيص
```dart
// تفعيل سجلات Firebase
await FirebaseDatabase.instance.setPersistenceEnabled(true);
```

## الخطوات التالية (اختيارية)

### ميزات إضافية يمكن تطويرها
- [ ] إرسال الصور والملفات
- [ ] الرد على الرسائل
- [ ] البحث في الرسائل
- [ ] الإشعارات الفورية
- [ ] رسائل خاصة بين المستخدمين
- [ ] إدارة الغرف للمشرفين

### تحسينات الأداء
- [ ] تحميل الرسائل بالتدريج (pagination)
- [ ] ضغط الصور قبل الإرسال
- [ ] تخزين مؤقت للرسائل

## الدعم

للمساعدة أو الإبلاغ عن مشاكل:
1. راجع سجلات التطبيق
2. تشغيل اختبارات النظام
3. التحقق من اتصال الإنترنت
4. مراجعة قواعد Firebase

---

**تم تطوير النظام بنجاح وهو جاهز للاستخدام! 🎉**
