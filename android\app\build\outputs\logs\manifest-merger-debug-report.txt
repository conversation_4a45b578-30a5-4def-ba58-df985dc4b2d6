-- Merging decision tree log ---
manifest
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:1-77:12
MERGED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:1-77:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3419449214fdf093a6d2234bb4bb8b3b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab7dfaa12e0ad8c11a2a33217b5afbad\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3a46b5e6c73bcb4fd7db0afd7dc4e1c\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41035b3623c9d6512b48b44a6e6c42f5\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\21f93b29571c2a4aeb374a1134f6bf3c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\524e9f0aac987b65b6426d820f87afcd\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88e5aec015684ff848801d13deb39b4e\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\36c242fe43375be7ca7d05ad85b025ad\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4242bab7db4b355911177f866aaaacc3\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a645eb404888b3ae7ce1f5034f89984d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf79248d2bad16944eab333efe4f7dc5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\153c3b9ee05e853b7143b54b98c4099c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ec0aa87f2a0e13af381f84a174b816b\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cf7946d3245a858a93e2197f0801eba5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c1fc2d2cceb7e6b0f5e3902d8bd474e\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\08cb923684f5e6b4142719def7bb13f6\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4993a7600a158388503f52e0a7b53013\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f74ecebd96b97a1209028fccacb71d3\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ae23a5ed9b17530c468634bc0c16e0e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec16421e3d16c763e68941db59180416\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b23de973096af6daee67e79955e88389\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\baf4fd5f4998d76fad0af831d4f1c2a2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0850f276e2d503f66dca85595d5ff46f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1eceb8e4291b9a25b66f173f6f7006fd\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:5-67
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:5-79
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:5-68
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:5:22-65
uses-permission#android.permission.VIBRATE
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:5-66
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:6:22-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:8:5-77
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:8:22-74
application
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:10:5-65:19
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec16421e3d16c763e68941db59180416\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec16421e3d16c763e68941db59180416\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:11:9-47
	android:icon
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:13:9-43
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:12:9-47
activity#com.legal2025.yamy.MainActivity
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:14:9-35:20
	android:launchMode
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:17:13-43
	android:hardwareAccelerated
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:21:13-47
	android:windowSoftInputMode
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:22:13-55
	android:exported
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:16:13-36
	android:configChanges
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:20:13-163
	android:theme
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:19:13-47
	android:taskAffinity
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:18:13-36
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:15:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:27:13-30:17
	android:resource
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:29:15-52
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:28:15-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:31:13-34:29
action#android.intent.action.MAIN
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:32:17-68
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:32:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:33:17-76
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:33:27-74
meta-data#flutterEmbedding
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:38:9-40:33
	android:value
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:40:13-30
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:39:13-44
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:43:9-49:19
	android:exported
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:45:13-37
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:44:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:46:13-48:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:47:17-78
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:47:25-75
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:52:9-54:55
	android:value
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:54:13-52
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:53:13-89
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:57:9-59:68
	android:resource
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:59:13-65
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:58:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:62:9-64:53
	android:resource
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:64:13-50
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:63:13-84
queries
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:71:5-76:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:72:9-75:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:73:13-72
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:73:21-70
data
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:74:13-50
	android:mimeType
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:74:19-48
uses-sdk
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3419449214fdf093a6d2234bb4bb8b3b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3419449214fdf093a6d2234bb4bb8b3b\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab7dfaa12e0ad8c11a2a33217b5afbad\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ab7dfaa12e0ad8c11a2a33217b5afbad\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3a46b5e6c73bcb4fd7db0afd7dc4e1c\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a3a46b5e6c73bcb4fd7db0afd7dc4e1c\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41035b3623c9d6512b48b44a6e6c42f5\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\41035b3623c9d6512b48b44a6e6c42f5\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\21f93b29571c2a4aeb374a1134f6bf3c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\21f93b29571c2a4aeb374a1134f6bf3c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\524e9f0aac987b65b6426d820f87afcd\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\524e9f0aac987b65b6426d820f87afcd\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88e5aec015684ff848801d13deb39b4e\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\88e5aec015684ff848801d13deb39b4e\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\36c242fe43375be7ca7d05ad85b025ad\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\36c242fe43375be7ca7d05ad85b025ad\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4242bab7db4b355911177f866aaaacc3\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4242bab7db4b355911177f866aaaacc3\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a645eb404888b3ae7ce1f5034f89984d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a645eb404888b3ae7ce1f5034f89984d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf79248d2bad16944eab333efe4f7dc5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf79248d2bad16944eab333efe4f7dc5\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\153c3b9ee05e853b7143b54b98c4099c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\153c3b9ee05e853b7143b54b98c4099c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ec0aa87f2a0e13af381f84a174b816b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ec0aa87f2a0e13af381f84a174b816b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cf7946d3245a858a93e2197f0801eba5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cf7946d3245a858a93e2197f0801eba5\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c1fc2d2cceb7e6b0f5e3902d8bd474e\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2c1fc2d2cceb7e6b0f5e3902d8bd474e\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\08cb923684f5e6b4142719def7bb13f6\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\08cb923684f5e6b4142719def7bb13f6\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4993a7600a158388503f52e0a7b53013\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4993a7600a158388503f52e0a7b53013\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f74ecebd96b97a1209028fccacb71d3\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0f74ecebd96b97a1209028fccacb71d3\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ae23a5ed9b17530c468634bc0c16e0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ae23a5ed9b17530c468634bc0c16e0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec16421e3d16c763e68941db59180416\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ec16421e3d16c763e68941db59180416\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b23de973096af6daee67e79955e88389\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b23de973096af6daee67e79955e88389\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\baf4fd5f4998d76fad0af831d4f1c2a2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\baf4fd5f4998d76fad0af831d4f1c2a2\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0850f276e2d503f66dca85595d5ff46f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0850f276e2d503f66dca85595d5ff46f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1eceb8e4291b9a25b66f173f6f7006fd\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1eceb8e4291b9a25b66f173f6f7006fd\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2314c30d0fcaa294bf6109669db900e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\15a93d521dfed6f1f45d40b4c805124e\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\921532473572e822d44e995d36a8f26d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4dab6a6c5d7b65e8e6bfd8cf2a895c9d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
