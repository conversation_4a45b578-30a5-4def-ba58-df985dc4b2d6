import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'logger.dart';
import '../services/realtime_pdf_service.dart';

/// أداة اختبار شاملة لـ Realtime Database
class RealtimeDBTester {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;

  /// اختبار شامل لـ Realtime Database
  static Future<Map<String, dynamic>> runFullTest() async {
    final results = <String, dynamic>{};

    if (kDebugMode) {
      print('🔥 بدء اختبار شامل لـ Realtime Database...');
    }

    // 1. فحص الاتصال
    results['connection'] = await _testConnection();

    // 2. فحص الصلاحيات
    results['permissions'] = await _testPermissions();

    // 3. اختبار إضافة PDF
    results['add_pdf'] = await _testAddPDF();

    // 4. اختبار قراءة PDF
    results['read_pdf'] = await _testReadPDF();

    // 5. اختبار حذف PDF
    results['delete_pdf'] = await _testDeletePDF();

    // 6. فحص البيانات في Firebase Console
    results['firebase_data'] = await _checkFirebaseData();

    if (kDebugMode) {
      print('✅ انتهى الاختبار الشامل');
      _printResults(results);
    }

    return results;
  }

  /// فحص الاتصال بـ Realtime Database
  static Future<Map<String, dynamic>> _testConnection() async {
    try {
      if (kDebugMode) print('🔍 فحص الاتصال بـ Realtime Database...');

      // محاولة قراءة بسيطة
      final ref = _database.ref().child('test');
      await ref.get();

      return {
        'status': 'success',
        'message': 'الاتصال بـ Realtime Database يعمل بشكل طبيعي',
        'database_url': _database.app.options.databaseURL,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في الاتصال بـ Realtime Database: $e',
      };
    }
  }

  /// فحص صلاحيات الكتابة والقراءة
  static Future<Map<String, dynamic>> _testPermissions() async {
    try {
      if (kDebugMode) print('🔍 فحص صلاحيات Realtime Database...');

      final testRef = _database.ref().child('test_permissions');
      final testData = {
        'test': true,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // اختبار الكتابة
      await testRef.set(testData);

      // اختبار القراءة
      final snapshot = await testRef.get();

      // حذف البيانات التجريبية
      await testRef.remove();

      return {
        'status': 'success',
        'message': 'صلاحيات القراءة والكتابة تعمل بشكل طبيعي',
        'data_written': snapshot.exists,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'مشكلة في صلاحيات Realtime Database: $e',
        'solution': 'تحقق من قواعد Database في Firebase Console',
      };
    }
  }

  /// اختبار إضافة PDF
  static Future<Map<String, dynamic>> _testAddPDF() async {
    try {
      if (kDebugMode) print('🔍 اختبار إضافة PDF...');

      final testPDFName = 'اختبار_${DateTime.now().millisecondsSinceEpoch}';

      final success = await RealtimePDFService.addPDF(
        name: testPDFName,
        url: 'https://www.google.com',
        category: 'اختبار',
        subjectId: 'test_subject',
        adminEmail: '<EMAIL>',
      );

      if (success) {
        return {
          'status': 'success',
          'message': 'تم إضافة PDF بنجاح',
          'test_pdf_name': testPDFName,
        };
      } else {
        return {'status': 'error', 'message': 'فشل في إضافة PDF'};
      }
    } catch (e) {
      return {'status': 'error', 'message': 'خطأ في اختبار إضافة PDF: $e'};
    }
  }

  /// اختبار قراءة PDF
  static Future<Map<String, dynamic>> _testReadPDF() async {
    try {
      if (kDebugMode) print('🔍 اختبار قراءة PDFs...');

      final pdfs = await RealtimePDFService.getPDFs(
        subjectId: 'test_subject',
        category: 'اختبار',
      );

      return {
        'status': 'success',
        'message': 'تم قراءة PDFs بنجاح',
        'pdfs_count': pdfs.length,
        'pdfs_found': pdfs.isNotEmpty,
      };
    } catch (e) {
      return {'status': 'error', 'message': 'خطأ في قراءة PDFs: $e'};
    }
  }

  /// اختبار حذف PDF
  static Future<Map<String, dynamic>> _testDeletePDF() async {
    try {
      if (kDebugMode) print('🔍 اختبار حذف PDF...');

      // أولاً نجلب PDFs الموجودة
      final pdfs = await RealtimePDFService.getPDFs(
        subjectId: 'test_subject',
        category: 'اختبار',
      );

      if (pdfs.isNotEmpty) {
        final firstPDF = pdfs.first;
        final success = await RealtimePDFService.deletePDF(
          subjectId: 'test_subject',
          category: 'اختبار',
          pdfId: firstPDF['id'],
        );

        if (success) {
          return {
            'status': 'success',
            'message': 'تم حذف PDF بنجاح',
            'deleted_pdf': firstPDF['name'],
          };
        } else {
          return {'status': 'error', 'message': 'فشل في حذف PDF'};
        }
      } else {
        return {'status': 'warning', 'message': 'لا توجد PDFs للحذف'};
      }
    } catch (e) {
      return {'status': 'error', 'message': 'خطأ في اختبار حذف PDF: $e'};
    }
  }

  /// فحص البيانات في Firebase Console
  static Future<Map<String, dynamic>> _checkFirebaseData() async {
    try {
      if (kDebugMode) print('🔍 فحص البيانات في Firebase...');

      final pdfsRef = _database.ref().child('pdfs');
      final snapshot = await pdfsRef.get();

      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        int totalSubjects = data.length;
        int totalCategories = 0;
        int totalPDFs = 0;

        data.forEach((subjectId, subjectData) {
          if (subjectData is Map) {
            totalCategories += subjectData.length;
            subjectData.forEach((category, categoryData) {
              if (categoryData is Map) {
                categoryData.forEach((pdfId, pdfData) {
                  if (pdfData is Map && pdfData['isActive'] == true) {
                    totalPDFs++;
                  }
                });
              }
            });
          }
        });

        return {
          'status': 'success',
          'message': 'تم العثور على بيانات في Firebase',
          'total_subjects': totalSubjects,
          'total_categories': totalCategories,
          'total_pdfs': totalPDFs,
          'data_structure_valid': true,
        };
      } else {
        return {
          'status': 'warning',
          'message': 'لا توجد بيانات PDFs في Firebase بعد',
          'suggestion': 'جرب إضافة ملف PDF أولاً',
        };
      }
    } catch (e) {
      return {'status': 'error', 'message': 'خطأ في فحص بيانات Firebase: $e'};
    }
  }

  /// طباعة النتائج
  static void _printResults(Map<String, dynamic> results) {
    AppLogger.info('نتائج اختبار Realtime Database:', 'RealtimeDBTester');
    AppLogger.info('=' * 60, 'RealtimeDBTester');

    results.forEach((testName, result) {
      final status = result['status'];
      final message = result['message'];

      switch (status) {
        case 'success':
          AppLogger.success('$testName: $message', 'RealtimeDBTester');
          break;
        case 'warning':
          AppLogger.warning('$testName: $message', 'RealtimeDBTester');
          break;
        case 'error':
          AppLogger.error('$testName: $message', 'RealtimeDBTester');
          break;
        default:
          AppLogger.info('$testName: $message', 'RealtimeDBTester');
      }

      // طباعة تفاصيل إضافية
      result.forEach((key, value) {
        if (key != 'status' && key != 'message') {
          AppLogger.info('   📊 $key: $value', 'RealtimeDBTester');
        }
      });

      if (result.containsKey('solution')) {
        AppLogger.info('   💡 الحل: ${result['solution']}', 'RealtimeDBTester');
      }
    });

    AppLogger.info('=' * 60, 'RealtimeDBTester');
  }

  /// اختبار سريع
  static Future<bool> quickTest() async {
    try {
      if (kDebugMode) print('⚡ اختبار سريع لـ Realtime Database...');

      // اختبار بسيط للاتصال والكتابة
      final testRef = _database.ref().child('quick_test');
      await testRef.set({
        'test': true,
        'time': DateTime.now().millisecondsSinceEpoch,
      });

      final snapshot = await testRef.get();
      await testRef.remove();

      if (kDebugMode) print('✅ الاختبار السريع نجح');
      return snapshot.exists;
    } catch (e) {
      if (kDebugMode) print('❌ الاختبار السريع فشل: $e');
      return false;
    }
  }

  /// فحص حالة Firebase Console
  static Future<Map<String, String>> getFirebaseInfo() async {
    try {
      final app = _database.app;
      return {
        'project_id': app.options.projectId,
        'database_url': app.options.databaseURL ?? 'غير محدد',
        'app_id': app.options.appId,
        'status': 'متصل',
      };
    } catch (e) {
      return {'status': 'خطأ: $e'};
    }
  }
}
