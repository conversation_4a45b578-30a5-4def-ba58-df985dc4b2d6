import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'models/subject.dart';
import 'models/community_post.dart';
import 'data/academic_data.dart';
import 'data/chat_data.dart';
import 'screens/subjects_screen.dart';
import 'screens/chat_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/verification_screen.dart';
// تم إزالة استدعاء AdminDashboardScreen - الصلاحيات مدمجة في الصفحات
import 'providers/theme_provider.dart';
import 'providers/firebase_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/admin_provider.dart';
import 'config/firebase_config.dart';
import 'services/notification_service.dart';
import 'services/admin_service.dart';
import 'services/community_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  await FirebaseConfig.initialize();

  // تهيئة خدمة الإشعارات
  await NotificationService.initialize();

  // تهيئة الأدمن الرئيسي
  await AdminService.initializeMainAdmin();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseProvider()),
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => AdminProvider()),
      ],
      child: const ShariaLawApp(),
    ),
  );
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'تطبيق الشريعة والقانون',
          debugShowCheckedModeBanner: false,
          theme: themeProvider.currentTheme.copyWith(
            textTheme: themeProvider.currentTheme.textTheme.apply(
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
          ),
          darkTheme: themeProvider.currentTheme.copyWith(
            textTheme: themeProvider.currentTheme.textTheme.apply(
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
          ),
          themeMode:
              themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar'), // العربية
          ],
          locale: const Locale('ar'),
          home: Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              // شاشة التحميل أثناء التهيئة
              if (!authProvider.isInitialized) {
                return _buildLoadingScreen(context);
              }

              // إذا كان المستخدم مسجل الدخول، عرض التطبيق الرئيسي
              if (authProvider.isAuthenticated) {
                return const MainScreen();
              }

              // إذا لم يكن مسجل الدخول، عرض شاشة تسجيل الدخول
              return const LoginScreen();
            },
          ),
          routes: {
            '/home': (context) => const MainScreen(),
            '/login': (context) => const LoginScreen(),
            '/verification':
                (context) => const VerificationScreen(
                  email: '',
                  password: '',
                  displayName: '',
                ),
          },
          builder: (context, child) {
            // تحديث شريط الحالة بناءً على الثيم
            SystemChrome.setSystemUIOverlayStyle(
              SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness:
                    themeProvider.isDarkMode
                        ? Brightness.light
                        : Brightness.dark,
                systemNavigationBarColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF0F172A)
                        : Colors.white,
                systemNavigationBarIconBrightness:
                    themeProvider.isDarkMode
                        ? Brightness.light
                        : Brightness.dark,
              ),
            );
            final direction = ui.TextDirection.rtl;
            return Directionality(textDirection: direction, child: child!);
          },
        );
      },
    );
  }

  Widget _buildLoadingScreen(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF667EEA),
                  const Color(0xFF764BA2),
                  const Color(0xFF6366F1),
                ],
              ),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // شعار التطبيق
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.balance,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 32),

                  // اسم التطبيق
                  Text(
                    'Legal 2025',
                    style: GoogleFonts.cairo(
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),

                  Text(
                    'تطبيق الشريعة والقانون',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 48),

                  // مؤشر التحميل
                  const SizedBox(
                    width: 50,
                    height: 50,
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(height: 24),

                  Text(
                    'جاري التحميل...',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ChatScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: _screens[_currentIndex],
          bottomNavigationBar: Container(
            height: 85,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.1),
                  blurRadius: 25,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: BottomNavigationBar(
                currentIndex: _currentIndex,
                onTap: (index) => setState(() => _currentIndex = index),
                backgroundColor: Colors.transparent,
                elevation: 0,
                selectedItemColor: const Color(0xFF6366F1),
                unselectedItemColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : Colors.grey[500],
                selectedLabelStyle: GoogleFonts.cairo(
                  fontSize: 11,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF6366F1),
                ),
                unselectedLabelStyle: GoogleFonts.cairo(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF64748B)
                          : Colors.grey[500],
                ),
                type: BottomNavigationBarType.fixed,
                items: [
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 0
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.home_rounded, size: 24),
                    ),
                    label: 'الرئيسية',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 1
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.groups_rounded, size: 24),
                    ),
                    label: 'المجتمع',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 2
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        children: [
                          const Icon(Icons.chat_bubble_rounded, size: 24),
                          if (ChatData.getTotalUnreadCount() > 0)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      Color(0xFFFF6B6B),
                                      Color(0xFFFF5252),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.5,
                                  ),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 18,
                                  minHeight: 18,
                                ),
                                child: Text(
                                  '${ChatData.getTotalUnreadCount()}',
                                  style: GoogleFonts.cairo(
                                    color: Colors.white,
                                    fontSize: 9,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    label: 'الدردشة',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 3
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.account_circle_rounded, size: 24),
                    ),
                    label: 'الملف الشخصي',
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? userRole;
  final bool isPinned;

  Post({
    required this.id,
    required this.authorName,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.userRole,
    required this.isPinned,
  });
}

// Home Screen
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<AcademicYear> academicYears = AcademicData.getAcademicYears();

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Modern Hero Header
                SliverToBoxAdapter(
                  child: Container(
                    height: 220,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(32),
                        bottomRight: Radius.circular(32),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Header Content
                        Positioned(
                          top: 20,
                          left: 20,
                          right: 20,
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(24),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.2),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'أهلاً وسهلاً! 🌟',
                                        style: GoogleFonts.cairo(
                                          fontSize: 24,
                                          fontWeight: FontWeight.w800,
                                          color: Colors.white,
                                          height: 1.2,
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                          height: 1.4,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // تم إزالة زر الأدمن - الصلاحيات مدمجة في الصفحات
                                // Notification Button
                                Container(
                                  width: 45,
                                  height: 45,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withValues(
                                        alpha: 0.3,
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: IconButton(
                                    onPressed: () => _showNotifications(),
                                    icon: Stack(
                                      children: [
                                        const Icon(
                                          Icons.notifications_rounded,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        Positioned(
                                          right: 2,
                                          top: 2,
                                          child: Container(
                                            width: 10,
                                            height: 10,
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFFF4757),
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                color: Colors.white,
                                                width: 1,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Spacing
                const SliverToBoxAdapter(child: SizedBox(height: 16)),

                // Section Title
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الفرق الدراسية',
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFFF1F5F9)
                                    : const Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                          ),
                        ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                ),

                // Academic Years List
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final year = academicYears[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildSimpleYearCard(year, index),
                      );
                    }, childCount: academicYears.length),
                  ),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return GestureDetector(
          onTap: () => _navigateToYear(year),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(int.parse(year.color.replaceFirst('#', '0xFF'))),
                  Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: themeProvider.isDarkMode ? 0.4 : 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        year.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 22,
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كلية الشريعة والقانون',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${year.semesters.length} فصول دراسية',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF6366F1)),
                SizedBox(width: 8),
                Text('الإشعارات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Text('لا توجد إشعارات جديدة')],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _navigateToYear(AcademicYear year) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)),
    );
  }

  // تم إزالة دالة التنقل للأدمن - الصلاحيات مدمجة في الصفحات
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  // متغيرات إنشاء المنشور
  // Post creation state
  bool _isCreatingPost = false;
  final TextEditingController _postController = TextEditingController();
  final FocusNode _postFocusNode = FocusNode();
  bool _isAnonymous = false;

  // Poll state
  bool _isPollMode = false;
  final List<TextEditingController> _pollControllers = [
    TextEditingController(),
    TextEditingController(),
  ];

  // Media handling
  final ImagePicker _imagePicker = ImagePicker();
  List<XFile> _selectedImages = [];
  PlatformFile? _selectedFile;

  // Media state
  bool _hasImage = false;
  bool _hasFile = false;

  // Comment controllers
  final Map<String, TextEditingController> _commentControllers = {};
  final Map<String, bool> _showComments = {};

  // Real data streams
  Stream<List<CommunityPost>>? _postsStream;
  Stream<CommunityStats>? _statsStream;

  @override
  void initState() {
    super.initState();
    _initializeStreams();
    _initializeCommunityService();
  }

  void _initializeStreams() {
    _postsStream = CommunityService.getPostsStream();
    _statsStream = CommunityService.getStatsStream();
  }

  void _initializeCommunityService() {
    // تهيئة خدمة المجتمع
    WidgetsBinding.instance.addPostFrameCallback((_) {
      CommunityService.initializeStats();
    });
  }

  @override
  void dispose() {
    _postController.dispose();
    _postFocusNode.dispose();
    for (var controller in _pollControllers) {
      controller.dispose();
    }
    for (var controller in _commentControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Modern Header
                SliverToBoxAdapter(
                  child: Container(
                    height: 140,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(24),
                        bottomRight: Radius.circular(24),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'مجتمع الطلاب',
                                      style: GoogleFonts.cairo(
                                        fontSize: 24,
                                        fontWeight: FontWeight.w800,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'شارك وتفاعل مع زملائك',
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.white.withValues(
                                          alpha: 0.9,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Notification Icon
                              Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: IconButton(
                                  onPressed: () => _showNotifications(),
                                  icon: Stack(
                                    children: [
                                      const Icon(
                                        Icons.notifications_rounded,
                                        color: Colors.white,
                                        size: 22,
                                      ),
                                      Positioned(
                                        right: 2,
                                        top: 2,
                                        child: Container(
                                          width: 8,
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFF4757),
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          // Quick Stats
                          StreamBuilder<CommunityStats>(
                            stream: _statsStream,
                            builder: (context, snapshot) {
                              final stats = snapshot.data;
                              return Row(
                                children: [
                                  _buildQuickStat(
                                    '${stats?.activeUsers ?? 0}',
                                    'طالب نشط',
                                  ),
                                  const SizedBox(width: 20),
                                  _buildQuickStat(
                                    '${stats?.todayPosts ?? 0}',
                                    'منشور اليوم',
                                  ),
                                  const SizedBox(width: 20),
                                  _buildQuickStat(
                                    '${stats?.todayComments ?? 0}',
                                    'تعليق اليوم',
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Create Post Section
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.black.withValues(alpha: 0.3)
                                  : Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Modern Facebook-style Post Creator
                        _buildModernPostCreator(),
                      ],
                    ),
                  ),
                ),

                // Posts List
                SliverToBoxAdapter(
                  child: StreamBuilder<List<CommunityPost>>(
                    stream: _postsStream,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(50),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      if (snapshot.hasError) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Text(
                              'خطأ في تحميل المنشورات',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        );
                      }

                      final posts = snapshot.data ?? [];

                      if (posts.isEmpty) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(50),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.post_add,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'لا توجد منشورات بعد',
                                  style: GoogleFonts.cairo(
                                    fontSize: 18,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'كن أول من ينشر في المجتمع!',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.grey[500],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      return RefreshIndicator(
                        onRefresh: () async {
                          setState(() {
                            _postsStream = CommunityService.getPostsStream();
                          });
                        },
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16),
                          itemCount: posts.length,
                          separatorBuilder:
                              (context, index) => const SizedBox(height: 12),
                          itemBuilder: (context, index) {
                            final post = posts[index];
                            return AnimatedContainer(
                              duration: Duration(
                                milliseconds: 300 + (index * 50),
                              ),
                              curve: Curves.easeOutBack,
                              child: _buildOptimizedPostCard(post),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w800,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 11,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildOptimizedPostCard(CommunityPost post) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: EdgeInsets.zero,
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Profile picture with gradient
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF3B82F6),
                            const Color(0xFF1D4ED8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF3B82F6,
                            ).withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          post.authorName.isNotEmpty ? post.authorName[0] : 'م',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Author info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                post.authorName,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFFF1F5F9)
                                          : const Color(0xFF1F2937),
                                ),
                              ),
                              if (post.authorId ==
                                  '<EMAIL>') ...[
                                const SizedBox(width: 6),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF10B981),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    'أدمن',
                                    style: GoogleFonts.cairo(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: const Color(0xFF6B7280),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _formatTimestamp(post.timestamp),
                                style: GoogleFonts.cairo(
                                  fontSize: 13,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF94A3B8)
                                          : const Color(0xFF6B7280),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                Icons.public,
                                size: 14,
                                color: const Color(0xFF6B7280),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // More options button
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFF3F4F6),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        onPressed: () => _showRealPostOptions(post),
                        icon: const Icon(
                          Icons.more_horiz,
                          color: Color(0xFF6B7280),
                        ),
                        iconSize: 20,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  post.content,
                  style: GoogleFonts.cairo(
                    fontSize: 15,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFFF1F5F9)
                            : const Color(0xFF374151),
                    height: 1.6,
                  ),
                ),
              ),

              // Poll widget
              if (post.poll != null) ...[
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildModernPollWidget(post),
                ),
              ],

              const SizedBox(height: 16),

              // Engagement stats
              if (post.likedBy.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEF4444),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.favorite,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '${post.likedBy.length}',
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Divider(height: 1, color: const Color(0xFFE5E7EB)),
                ),
                const SizedBox(height: 12),
              ],

              // Action buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildModernActionButton(
                        icon:
                            post.likedBy.contains(
                                  CommunityService.currentUserId,
                                )
                                ? Icons.favorite
                                : Icons.favorite_border,
                        label: 'إعجاب',
                        color:
                            post.likedBy.contains(
                                  CommunityService.currentUserId,
                                )
                                ? const Color(0xFFEF4444)
                                : const Color(0xFF6B7280),
                        onTap: () => _toggleRealLike(post),
                      ),
                    ),
                    Expanded(
                      child: _buildModernActionButton(
                        icon: Icons.chat_bubble_outline,
                        label: 'تعليق',
                        color: const Color(0xFF6B7280),
                        onTap: () {
                          setState(() {
                            _showComments[post.id] =
                                !(_showComments[post.id] ?? false);
                            if (!_commentControllers.containsKey(post.id)) {
                              _commentControllers[post.id] =
                                  TextEditingController();
                            }
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: _buildModernActionButton(
                        icon: Icons.share_outlined,
                        label: 'مشاركة',
                        color: const Color(0xFF6B7280),
                        onTap: () => _shareRealPost(post),
                      ),
                    ),
                  ],
                ),
              ),

              // Comments section
              if (_showComments[post.id] == true) ...[
                const SizedBox(height: 16),
                _buildModernCommentsSection(post.id),
              ],

              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernPollWidget(CommunityPost post) {
    final poll = post.poll!;
    final options = List<String>.from(poll['options'] ?? []);
    final votes = Map<String, int>.from(poll['votes'] ?? {});
    final totalVotes = votes.values.fold(0, (sum, count) => sum + count);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [const Color(0xFFF8FAFC), const Color(0xFFEFF6FF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.poll, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  'استطلاع رأي',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ...options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              final voteCount = votes[index.toString()] ?? 0;
              final percentage =
                  totalVotes > 0 ? (voteCount / totalVotes * 100) : 0;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: InkWell(
                  onTap: () => _voteInFastPoll(post.id, index),
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            percentage > 0
                                ? const Color(0xFF3B82F6).withValues(alpha: 0.3)
                                : const Color(0xFFE2E8F0),
                      ),
                      boxShadow: [
                        if (percentage > 0)
                          BoxShadow(
                            color: const Color(
                              0xFF3B82F6,
                            ).withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Progress bar background
                        if (percentage > 0)
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                color: const Color(
                                  0xFF3B82F6,
                                ).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              width: double.infinity,
                            ),
                          ),

                        // Progress bar
                        if (percentage > 0)
                          Positioned.fill(
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: percentage / 100,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFF3B82F6,
                                  ).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),

                        // Content
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                option,
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF374151),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF3B82F6),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '$voteCount (${percentage.toStringAsFixed(0)}%)',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),

            if (totalVotes > 0) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.how_to_vote,
                    size: 16,
                    color: const Color(0xFF6B7280),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'إجمالي الأصوات: $totalVotes',
                    style: GoogleFonts.cairo(
                      fontSize: 13,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModernCommentsSection(String postId) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        children: [
          // Add comment input
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF10B981),
                        const Color(0xFF059669),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Center(
                    child: Text(
                      'أ',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      border: Border.all(color: const Color(0xFFE2E8F0)),
                    ),
                    child: TextField(
                      controller: _commentControllers[postId],
                      decoration: InputDecoration(
                        hintText: 'اكتب تعليقاً...',
                        hintStyle: GoogleFonts.cairo(
                          fontSize: 14,
                          color: const Color(0xFF9CA3AF),
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: GoogleFonts.cairo(fontSize: 14),
                      onSubmitted: (value) => _addRealComment(postId),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: () => _addRealComment(postId),
                    icon: const Icon(Icons.send, color: Colors.white),
                    iconSize: 18,
                  ),
                ),
              ],
            ),
          ),

          // Comments list
          StreamBuilder<List<CommunityComment>>(
            stream: CommunityService.getCommentsStream(postId),
            builder: (context, snapshot) {
              if (!snapshot.hasData) return const SizedBox.shrink();

              final comments = snapshot.data!;
              if (comments.isEmpty) return const SizedBox.shrink();

              return Column(
                children:
                    comments.take(5).map((comment) {
                      return Container(
                        margin: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          bottom: 12,
                        ),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: const Color(0xFFE2E8F0)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF8B5CF6),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Center(
                                    child: Text(
                                      comment.authorName.isNotEmpty
                                          ? comment.authorName[0]
                                          : 'م',
                                      style: GoogleFonts.cairo(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  comment.authorName,
                                  style: GoogleFonts.cairo(
                                    fontSize: 13,
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF374151),
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  _formatTimestamp(comment.timestamp),
                                  style: GoogleFonts.cairo(
                                    fontSize: 11,
                                    color: const Color(0xFF9CA3AF),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              comment.content,
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                color: const Color(0xFF374151),
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              );
            },
          ),

          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Future<void> _voteInFastPoll(String postId, int optionIndex) async {
    try {
      _showQuickSnackBar('جاري التصويت...', duration: 1);

      final success = await CommunityService.voteInPoll(
        postId: postId,
        optionIndex: optionIndex,
      );

      if (success) {
        _showQuickSnackBar('تم تسجيل صوتك! 🗳️');
      } else {
        _showQuickSnackBar('خطأ في التصويت', isError: true);
      }
    } catch (e) {
      _showQuickSnackBar('خطأ في التصويت', isError: true);
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Widget _buildModernPostCreator() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header Row
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: const Color(0xFF3B82F6),
                    child: Text(
                      'أ',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _isCreatingPost = true;
                        });
                        _postFocusNode.requestFocus();
                      },
                      child: Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color:
                                    _isCreatingPost
                                        ? const Color(0xFF3B82F6)
                                        : themeProvider.isDarkMode
                                        ? const Color(0xFF475569)
                                        : const Color(0xFFE5E7EB),
                                width: _isCreatingPost ? 2 : 1,
                              ),
                            ),
                            child: Text(
                              'ما الذي تريد مشاركته؟',
                              style: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF94A3B8)
                                        : const Color(0xFF9CA3AF),
                                fontSize: 16,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),

              // Expanded Content
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child:
                    _isCreatingPost
                        ? _buildExpandedPostContent()
                        : const SizedBox.shrink(),
              ),

              // Action Buttons Row
              if (!_isCreatingPost) ...[
                const SizedBox(height: 12),
                const Divider(height: 1, color: Color(0xFFE5E7EB)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.photo_library,
                        label: 'صورة',
                        color: const Color(0xFF10B981),
                        isActive: _hasImage,
                        onTap: _toggleImageMode,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.poll,
                        label: 'استطلاع',
                        color: const Color(0xFF8B5CF6),
                        isActive: _isPollMode,
                        onTap: _togglePollMode,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.attach_file,
                        label: 'ملف',
                        color: const Color(0xFFEF4444),
                        isActive: _hasFile,
                        onTap: _toggleFileMode,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildExpandedPostContent() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          // Text Input Area
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF334155)
                          : const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF475569)
                            : const Color(0xFFE5E7EB),
                  ),
                ),
                child: TextField(
                  controller: _postController,
                  focusNode: _postFocusNode,
                  maxLines: null,
                  minLines: 3,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    height: 1.5,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFFF1F5F9)
                            : const Color(0xFF1F2937),
                  ),
                  decoration: InputDecoration(
                    hintText: 'شاركنا أفكارك وآرائك...',
                    hintStyle: GoogleFonts.cairo(
                      fontSize: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF94A3B8)
                              : const Color(0xFF9CA3AF),
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) => setState(() {}),
                ),
              );
            },
          ),

          // Media Attachments
          if (_hasImage) _buildImageAttachment(),
          if (_hasFile) _buildFileAttachment(),
          if (_isPollMode) _buildPollCreation(),

          const SizedBox(height: 16),

          // Bottom Controls
          Row(
            children: [
              // Anonymous Toggle
              _buildAnonymousToggle(),

              const Spacer(),

              // Action Buttons
              TextButton(
                onPressed: _cancelPost,
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(
                    color: const Color(0xFF6B7280),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              ElevatedButton(
                onPressed: _canPublishPost() ? _publishPost : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3B82F6),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'نشر',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border:
                isActive
                    ? Border.all(color: color.withValues(alpha: 0.3))
                    : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isActive ? color : const Color(0xFF6B7280),
                size: 20,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: isActive ? color : const Color(0xFF6B7280),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _toggleImageMode() async {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = false; // إلغاء الاستطلاع عند اختيار صورة
    });

    if (!_hasImage) {
      await _selectImages();
    } else {
      setState(() {
        _hasImage = false;
        _selectedImages.clear();
      });
    }
    _postFocusNode.requestFocus();
  }

  Future<void> _selectImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages = images;
          _hasImage = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الصور: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    }
  }

  void _togglePollMode() {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = !_isPollMode;
      if (_isPollMode) {
        _hasImage = false;
        _hasFile = false;
        _selectedImages.clear();
        _selectedFile = null;
        // التأكد من وجود خيارين على الأقل
        while (_pollControllers.length < 2) {
          _pollControllers.add(TextEditingController());
        }
      }
    });
    _postFocusNode.requestFocus();
  }

  Future<void> _toggleFileMode() async {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = false;
    });

    if (!_hasFile) {
      await _selectFile();
    } else {
      setState(() {
        _hasFile = false;
        _selectedFile = null;
      });
    }
    _postFocusNode.requestFocus();
  }

  Future<void> _selectFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'ppt',
          'pptx',
          'txt',
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFile = result.files.first;
          _hasFile = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الملف: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    }
  }

  void _cancelPost() {
    setState(() {
      _isCreatingPost = false;
      _isPollMode = false;
      _hasImage = false;
      _hasFile = false;
      _isAnonymous = false;
      _selectedImages.clear();
      _selectedFile = null;
      _postController.clear();
      for (var controller in _pollControllers) {
        controller.clear();
      }
    });
  }

  Widget _buildImageAttachment() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: const Color(0xFFF0FDF4),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child:
          _selectedImages.isNotEmpty
              ? _buildImageGrid()
              : _buildImagePlaceholder(),
    );
  }

  Widget _buildImageGrid() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: Stack(
        children: [
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(8),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _selectedImages.length == 1 ? 1 : 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1.2,
            ),
            itemCount: _selectedImages.length > 4 ? 4 : _selectedImages.length,
            itemBuilder: (context, index) {
              final image = _selectedImages[index];
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(image.path), // For web
                        fit: BoxFit.cover,
                        onError: (error, stackTrace) {
                          // Fallback for web
                        },
                      ),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (index == 3 && _selectedImages.length > 4)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.black.withValues(alpha: 0.6),
                      ),
                      child: Center(
                        child: Text(
                          '+${_selectedImages.length - 3}',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap:
                  () => setState(() {
                    _hasImage = false;
                    _selectedImages.clear();
                  }),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(Icons.close, size: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image, size: 32, color: const Color(0xFF10B981)),
            const SizedBox(height: 4),
            Text(
              'صور مرفقة',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: const Color(0xFF10B981),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPollCreation() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFFF8FAFF), const Color(0xFFF3F4F6)],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF8B5CF6).withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.poll_rounded,
                  color: const Color(0xFF8B5CF6),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إنشاء استطلاع تفاعلي',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      'اطرح سؤال < واحصل على آراء المجتمع',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => setState(() => _isPollMode = false),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.close_rounded,
                    size: 18,
                    color: const Color(0xFFEF4444),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Poll Options
          ...List.generate(_pollControllers.length, (index) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF8B5CF6),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              _pollControllers[index].text.isNotEmpty
                                  ? const Color(0xFF8B5CF6)
                                  : const Color(0xFFE5E7EB),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _pollControllers[index],
                        decoration: InputDecoration(
                          hintText: 'اكتب الخيار ${index + 1}...',
                          hintStyle: GoogleFonts.cairo(
                            color: const Color(0xFF9CA3AF),
                            fontSize: 14,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: const Color(0xFF1F2937),
                        ),
                        onChanged: (value) => setState(() {}),
                      ),
                    ),
                  ),
                  if (_pollControllers.length > 2 && index >= 2) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () => _removePollOption(index),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.remove_rounded,
                          color: const Color(0xFFEF4444),
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),

          // Add Option Button
          if (_pollControllers.length < 5)
            GestureDetector(
              onTap: _addPollOption,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                    style: BorderStyle.solid,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_rounded,
                      color: const Color(0xFF8B5CF6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'إضافة خيار جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF8B5CF6),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 12),

          // Poll Info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFF3B82F6),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'سيتمكن الأعضاء من التصويت مرة واحدة فقط',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: const Color(0xFF3B82F6),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnonymousToggle() {
    return GestureDetector(
      onTap: () => setState(() => _isAnonymous = !_isAnonymous),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              _isAnonymous
                  ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                _isAnonymous
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFFE5E7EB),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _isAnonymous
                  ? Icons.visibility_off
                  : Icons.visibility_off_outlined,
              size: 16,
              color:
                  _isAnonymous
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF6B7280),
            ),
            const SizedBox(width: 6),
            Text(
              'مجهول',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    _isAnonymous
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addPollOption() {
    if (_pollControllers.length < 5) {
      setState(() {
        _pollControllers.add(TextEditingController());
      });
    }
  }

  void _removePollOption(int index) {
    if (_pollControllers.length > 2 && index >= 2) {
      setState(() {
        _pollControllers[index].dispose();
        _pollControllers.removeAt(index);
      });
    }
  }

  Widget _buildFileAttachment() {
    final fileColor = _getFileColor(_selectedFile?.extension ?? '');

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            fileColor.withValues(alpha: 0.05),
            fileColor.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: fileColor.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: fileColor.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الملف مع تأثيرات حديثة
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [fileColor, fileColor.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: fileColor.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              _getFileIcon(_selectedFile?.extension ?? ''),
              size: 28,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),

          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedFile?.name ?? 'ملف مرفق',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: const Color(0xFF1F2937),
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: fileColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        (_selectedFile?.extension ?? '').toUpperCase(),
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: fileColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatFileSize(_selectedFile?.size ?? 0),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6B7280),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // أزرار التحكم
          Column(
            children: [
              // زر التحميل/الفتح
              GestureDetector(
                onTap: () => _openFile(_selectedFile!),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: fileColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.open_in_new_rounded,
                    size: 20,
                    color: fileColor,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // زر الحذف
              GestureDetector(
                onTap:
                    () => setState(() {
                      _hasFile = false;
                      _selectedFile = null;
                    }),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.close_rounded,
                    size: 20,
                    color: Color(0xFFEF4444),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getFileColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFEF4444);
      case 'doc':
      case 'docx':
        return const Color(0xFF2563EB);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF059669);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD97706);
      case 'txt':
        return const Color(0xFF6B7280);
      default:
        return const Color(0xFF8B5CF6);
    }
  }

  void _openFile(PlatformFile file) {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                Text(
                  'خيارات الملف',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 20),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFileOption(
                      icon: Icons.visibility_rounded,
                      label: 'عرض',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم فتح الملف للعرض')),
                        );
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.download_rounded,
                      label: 'تحميل',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم بدء التحميل')),
                        );
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.share_rounded,
                      label: 'مشاركة',
                      color: const Color(0xFF8B5CF6),
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('تم فتح خيارات المشاركة')),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildFileOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.attach_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  Future<void> _publishPost() async {
    await _createRealPost();
  }

  Future<void> _createRealPost() async {
    final content = _postController.text.trim();

    if (content.isEmpty && !_hasImage && !_hasFile && !_isPollMode) {
      _showQuickSnackBar('يرجى إضافة محتوى للمنشور', isError: true);
      return;
    }

    // إظهار مؤشر التحميل
    _showQuickSnackBar('جاري النشر...', duration: 1);

    try {
      String finalContent = content;

      // إضافة محتوى الاستطلاع
      if (_isPollMode) {
        final pollOptions =
            _pollControllers
                .map((c) => c.text.trim())
                .where((text) => text.isNotEmpty)
                .toList();

        if (pollOptions.length >= 2) {
          finalContent +=
              '\n\n📊 استطلاع:\n${pollOptions.map((o) => '• $o').join('\n')}';
        }
      }

      // إضافة الملفات والصور
      if (_selectedFile != null) {
        finalContent += '\n\n📎 ${_selectedFile!.name}';
      }
      if (_selectedImages.isNotEmpty) {
        finalContent += '\n\n📷 ${_selectedImages.length} صورة';
      }

      // إنشاء المنشور
      await CommunityService.createPost(
        content: finalContent,
        imageUrls: [],
        poll: _isPollMode ? _createPollData() : null,
      );

      // إعادة تعيين النموذج
      _cancelPost();

      // رسالة نجاح
      _showQuickSnackBar('تم النشر بنجاح! 🎉');
    } catch (e) {
      _showQuickSnackBar('خطأ في النشر', isError: true);
    }
  }

  void _showQuickSnackBar(
    String message, {
    bool isError = false,
    int duration = 2,
  }) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: GoogleFonts.cairo()),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: duration),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Map<String, dynamic>? _createPollData() {
    if (!_isPollMode) return null;

    final options =
        _pollControllers
            .map((controller) => controller.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();

    if (options.length < 2) return null;

    return {
      'question': 'استطلاع رأي',
      'options': options,
      'votes': {for (int i = 0; i < options.length; i++) i.toString(): 0},
    };
  }

  bool _canPublishPost() {
    // يمكن النشر إذا كان هناك نص أو صورة أو ملف أو استطلاع مكتمل
    if (_postController.text.trim().isNotEmpty) return true;
    if (_hasImage) return true;
    if (_hasFile) return true;
    if (_isPollMode) {
      // التحقق من وجود خيارين على الأقل مع محتوى
      int validOptions = 0;
      for (var controller in _pollControllers) {
        if (controller.text.trim().isNotEmpty) {
          validOptions++;
        }
      }
      return validOptions >= 2;
    }
    return false;
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الإشعارات',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w700),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildNotificationItem(
                  'أحمد علي',
                  'أعجب بمنشورك',
                  '5 دقائق',
                  Icons.favorite,
                  const Color(0xFFEF4444),
                ),
                _buildNotificationItem(
                  'فاطمة محمد',
                  'علقت على منشورك',
                  '10 دقائق',
                  Icons.chat_bubble,
                  const Color(0xFF6366F1),
                ),
                _buildNotificationItem(
                  'د. محمد أحمد',
                  'نشر منشوراً جديداً',
                  '30 دقيقة',
                  Icons.post_add,
                  const Color(0xFF10B981),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Widget _buildNotificationItem(
    String name,
    String action,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: name,
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      TextSpan(
                        text: ' $action',
                        style: GoogleFonts.cairo(
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  time,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Real post interaction methods
  Future<void> _toggleRealLike(CommunityPost post) async {
    try {
      await CommunityService.toggleLike(post.id);
      // The UI will update automatically through the stream
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تبديل الإعجاب'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareRealPost(CommunityPost post) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ رابط المنشور'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showRealPostOptions(CommunityPost post) {
    final isOwner = post.authorId == CommunityService.currentUserId;
    final isAdmin =
        CommunityService.currentUser?.email == '<EMAIL>';

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),

                // Title
                Text(
                  'خيارات المنشور',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 20),

                // Copy text option
                _buildOptionTile(
                  icon: Icons.copy_outlined,
                  title: 'نسخ النص',
                  color: const Color(0xFF3B82F6),
                  onTap: () {
                    Navigator.pop(context);
                    Clipboard.setData(ClipboardData(text: post.content));
                    _showQuickSnackBar('تم نسخ النص');
                  },
                ),

                // Share option
                _buildOptionTile(
                  icon: Icons.share_outlined,
                  title: 'مشاركة المنشور',
                  color: const Color(0xFF10B981),
                  onTap: () {
                    Navigator.pop(context);
                    _shareRealPost(post);
                  },
                ),

                // Edit option (for owner only)
                if (isOwner) ...[
                  _buildOptionTile(
                    icon: Icons.edit_outlined,
                    title: 'تعديل المنشور',
                    color: const Color(0xFF8B5CF6),
                    onTap: () {
                      Navigator.pop(context);
                      _showQuickSnackBar('ميزة التعديل قريباً');
                    },
                  ),
                ],

                // Delete option (for owner and admin)
                if (isOwner || isAdmin) ...[
                  _buildOptionTile(
                    icon: Icons.delete_outline,
                    title: 'حذف المنشور',
                    color: const Color(0xFFEF4444),
                    onTap: () {
                      Navigator.pop(context);
                      _confirmDeletePost(post);
                    },
                  ),
                ],

                // Report option (for others)
                if (!isOwner) ...[
                  _buildOptionTile(
                    icon: Icons.flag_outlined,
                    title: 'إبلاغ عن المنشور',
                    color: const Color(0xFFEF4444),
                    onTap: () {
                      Navigator.pop(context);
                      _showQuickSnackBar('تم إرسال البلاغ');
                    },
                  ),
                ],

                const SizedBox(height: 10),
              ],
            ),
          ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF374151),
        ),
      ),
      onTap: onTap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  void _confirmDeletePost(CommunityPost post) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(
              'حذف المنشور',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من حذف هذا المنشور؟ لا يمكن التراجع عن هذا الإجراء.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _deletePost(post);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFEF4444),
                  foregroundColor: Colors.white,
                ),
                child: Text('حذف', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Future<void> _deletePost(CommunityPost post) async {
    try {
      _showQuickSnackBar('جاري الحذف...', duration: 1);

      await CommunityService.deletePost(post.id);

      _showQuickSnackBar('تم حذف المنشور بنجاح');
    } catch (e) {
      _showQuickSnackBar('خطأ في حذف المنشور', isError: true);
    }
  }

  Future<void> _addRealComment(String postId) async {
    final controller = _commentControllers[postId];
    if (controller == null || controller.text.trim().isEmpty) return;

    try {
      await CommunityService.addComment(
        postId: postId,
        content: controller.text.trim(),
      );
      controller.clear();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة التعليق'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.userModel != null) {
      _nameController.text = authProvider.userModel!.displayName;
      _emailController.text = authProvider.userModel!.email;
    } else if (authProvider.firebaseUser != null) {
      _nameController.text = authProvider.firebaseUser!.displayName ?? 'مستخدم';
      _emailController.text = authProvider.firebaseUser!.email ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  // دالة تعديل الملف الشخصي
  Future<void> _showEditProfileDialog() async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تعديل الملف الشخصي',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  labelStyle: GoogleFonts.cairo(),
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.person),
                ),
                style: GoogleFonts.cairo(),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  labelStyle: GoogleFonts.cairo(),
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.email),
                ),
                style: GoogleFonts.cairo(),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                await _updateProfile();
                if (mounted) {
                  navigator.pop();
                }
              },
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(color: const Color(0xFF6366F1)),
              ),
            ),
          ],
        );
      },
    );
  }

  // دالة تحديث الملف الشخصي
  Future<void> _updateProfile() async {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '✅ تم تحديث الملف الشخصي بنجاح',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // دالة تسجيل الخروج
  Future<void> _handleSignOut() async {
    // عرض dialog للتأكيد
    final bool? shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تسجيل الخروج',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );

    if (shouldSignOut == true && mounted) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.signOut();

      if (mounted) {
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/login', (route) => false);
      }
    }
  }

  // الحصول على اسم المستخدم المناسب
  String _getDisplayName() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.userModel != null) {
      return authProvider.userModel!.displayName;
    } else if (authProvider.firebaseUser != null) {
      return authProvider.firebaseUser!.displayName ?? 'مستخدم';
    }
    return _nameController.text.isNotEmpty
        ? _nameController.text
        : 'مستخدم التطبيق';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Profile Header
                  Container(
                    height: 280,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(32),
                        bottomRight: Radius.circular(32),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background Pattern
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(32),
                                bottomRight: Radius.circular(32),
                              ),
                            ),
                          ),
                        ),
                        // Profile Content
                        Positioned(
                          top: 40,
                          left: 24,
                          right: 24,
                          child: Column(
                            children: [
                              // Profile Avatar
                              Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(60),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.person_rounded,
                                  size: 60,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 20),
                              // Name
                              Text(
                                _getDisplayName(),
                                style: GoogleFonts.cairo(
                                  fontSize: 28,
                                  fontWeight: FontWeight.w800,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Email
                              Text(
                                _emailController.text,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Role
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'طالب مسجل',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),
                              // University
                              Text(
                                'كلية الشريعة والقانون',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                  // Settings
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        _buildSettingItem(
                          icon: Icons.edit,
                          title: 'تعديل الملف الشخصي',
                          onTap: _showEditProfileDialog,
                        ),

                        _buildSettingItem(
                          icon: Icons.download,
                          title: 'التحميلات',
                          onTap: () {},
                        ),
                        Consumer<ThemeProvider>(
                          builder: (context, themeProvider, child) {
                            return _buildSettingItem(
                              icon:
                                  themeProvider.isDarkMode
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                              title: 'الوضع المظلم',
                              trailing: Switch(
                                value: themeProvider.isDarkMode,
                                onChanged: (value) {
                                  themeProvider.toggleTheme();
                                },
                                activeColor: const Color(0xFF6366F1),
                              ),
                              onTap: () {
                                themeProvider.toggleTheme();
                              },
                            );
                          },
                        ),

                        _buildSettingItem(
                          icon: Icons.logout,
                          title: 'تسجيل الخروج',
                          onTap: _handleSignOut,
                          isDestructive: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color:
                    (themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08)),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155).withValues(alpha: 0.5)
                      : const Color(0xFFE5E7EB).withValues(alpha: 0.5),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Icon Container
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: (isDestructive
                                ? Colors.red
                                : const Color(0xFF6366F1))
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        color:
                            isDestructive
                                ? Colors.red
                                : const Color(0xFF6366F1),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Title
                    Expanded(
                      child: Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              isDestructive
                                  ? Colors.red
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFFF1F5F9)
                                      : const Color(0xFF1F2937)),
                        ),
                      ),
                    ),
                    // Trailing
                    if (trailing != null) ...[
                      trailing,
                    ] else ...[
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF64748B)
                                : const Color(0xFF9CA3AF),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
