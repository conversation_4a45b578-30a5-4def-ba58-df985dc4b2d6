# Gradle JVM settings
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=1G -XX:ReservedCodeCacheSize=256m -Dfile.encoding=UTF-8

# Android settings
android.useAndroidX=true
android.enableJetifier=true
android.suppressUnsupportedCompileSdk=34,35

# Kotlin settings - disable daemon to avoid connection issues
kotlin.incremental=false
kotlin.compiler.execution.strategy=in-process
kotlin.daemon.useFallbackStrategy=true

# Gradle performance settings
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=false
org.gradle.daemon=true

# Fix path issues
org.gradle.unsafe.configuration-cache=false
