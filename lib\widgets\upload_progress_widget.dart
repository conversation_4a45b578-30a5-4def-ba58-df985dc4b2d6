import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../services/upload_manager.dart';

class UploadProgressWidget extends StatefulWidget {
  final String taskId;
  final String fileName;
  final VoidCallback? onCancel;
  final VoidCallback? onRetry;

  const UploadProgressWidget({
    super.key,
    required this.taskId,
    required this.fileName,
    this.onCancel,
    this.onRetry,
  });

  @override
  State<UploadProgressWidget> createState() => _UploadProgressWidgetState();
}

class _UploadProgressWidgetState extends State<UploadProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final UploadManager _uploadManager = UploadManager();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startProgressTracking();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _startProgressTracking() {
    // تحديث التقدم كل 100ms
    Future.doWhile(() async {
      await Future.delayed(const Duration(milliseconds: 100));
      if (mounted) {
        setState(() {});
        final status = _uploadManager.getTaskStatus(widget.taskId);
        return status == UploadTaskStatus.uploading || status == UploadTaskStatus.pending;
      }
      return false;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final status = _uploadManager.getTaskStatus(widget.taskId);
        final progress = _uploadManager.getTaskProgress(widget.taskId) ?? 0.0;

        return FadeTransition(
          opacity: _fadeAnimation,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: themeProvider.isDarkMode
                  ? const Color(0xFF1E293B)
                  : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getStatusColor(status).withValues(alpha: 0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    _buildStatusIcon(status),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.fileName,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getStatusText(status, progress),
                            style: GoogleFonts.cairo(
                              fontSize: 12,
                              color: _getStatusColor(status),
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildActionButton(status, themeProvider),
                  ],
                ),
                if (status == UploadTaskStatus.uploading) ...[
                  const SizedBox(height: 12),
                  _buildProgressBar(progress, themeProvider),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusIcon(UploadTaskStatus? status) {
    IconData icon;
    Color color;

    switch (status) {
      case UploadTaskStatus.pending:
        icon = Icons.schedule;
        color = Colors.orange;
        break;
      case UploadTaskStatus.uploading:
        icon = Icons.cloud_upload;
        color = const Color(0xFF10B981);
        break;
      case UploadTaskStatus.completed:
        icon = Icons.check_circle;
        color = const Color(0xFF10B981);
        break;
      case UploadTaskStatus.failed:
        icon = Icons.error;
        color = Colors.red;
        break;
      case UploadTaskStatus.cancelled:
        icon = Icons.cancel;
        color = Colors.grey;
        break;
      default:
        icon = Icons.help;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }

  Widget _buildActionButton(UploadTaskStatus? status, ThemeProvider themeProvider) {
    switch (status) {
      case UploadTaskStatus.uploading:
      case UploadTaskStatus.pending:
        return IconButton(
          onPressed: () {
            _uploadManager.cancelUpload(widget.taskId);
            widget.onCancel?.call();
          },
          icon: Icon(
            Icons.close,
            color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[600],
            size: 20,
          ),
        );

      case UploadTaskStatus.failed:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: widget.onRetry,
              icon: const Icon(
                Icons.refresh,
                color: Color(0xFF10B981),
                size: 20,
              ),
            ),
            IconButton(
              onPressed: widget.onCancel,
              icon: Icon(
                Icons.close,
                color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                size: 20,
              ),
            ),
          ],
        );

      case UploadTaskStatus.completed:
        return IconButton(
          onPressed: widget.onCancel,
          icon: Icon(
            Icons.check,
            color: const Color(0xFF10B981),
            size: 20,
          ),
        );

      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildProgressBar(double progress, ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'جاري الرفع...',
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: themeProvider.isDarkMode
                    ? Colors.grey[400]
                    : Colors.grey[600],
              ),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: GoogleFonts.cairo(
                fontSize: 11,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF10B981),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: themeProvider.isDarkMode
                ? Colors.grey[700]
                : Colors.grey[200],
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
            minHeight: 6,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(UploadTaskStatus? status) {
    switch (status) {
      case UploadTaskStatus.pending:
        return Colors.orange;
      case UploadTaskStatus.uploading:
        return const Color(0xFF10B981);
      case UploadTaskStatus.completed:
        return const Color(0xFF10B981);
      case UploadTaskStatus.failed:
        return Colors.red;
      case UploadTaskStatus.cancelled:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(UploadTaskStatus? status, double progress) {
    switch (status) {
      case UploadTaskStatus.pending:
        return 'في انتظار الرفع...';
      case UploadTaskStatus.uploading:
        return 'جاري الرفع... ${(progress * 100).toInt()}%';
      case UploadTaskStatus.completed:
        return 'تم الرفع بنجاح';
      case UploadTaskStatus.failed:
        return 'فشل في الرفع';
      case UploadTaskStatus.cancelled:
        return 'تم الإلغاء';
      default:
        return 'غير معروف';
    }
  }
}

/// Widget لعرض قائمة مهام الرفع
class UploadQueueWidget extends StatefulWidget {
  const UploadQueueWidget({super.key});

  @override
  State<UploadQueueWidget> createState() => _UploadQueueWidgetState();
}

class _UploadQueueWidgetState extends State<UploadQueueWidget> {
  final UploadManager _uploadManager = UploadManager();

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final stats = _uploadManager.getStats();
        final totalTasks = stats['activeTasks'] + stats['queuedTasks'];

        if (totalTasks == 0) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeProvider.isDarkMode
                ? const Color(0xFF1E293B)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.cloud_upload,
                    color: const Color(0xFF10B981),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'مهام الرفع',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: themeProvider.isDarkMode
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '$totalTasks مهمة',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'نشط: ${stats['activeTasks']} | في الطابور: ${stats['queuedTasks']}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
