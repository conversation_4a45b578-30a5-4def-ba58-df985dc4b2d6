import 'dart:convert';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'real_email_service.dart';
import '../utils/logger.dart';

/// خدمة التحقق من البريد الإلكتروني باستخدام رابط التحقق
class EmailLinkVerificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const String _collectionName = 'email_verifications';

  // مدة انتهاء صلاحية الرابط (30 دقيقة)
  static const Duration _linkExpiration = Duration(minutes: 30);

  /// إنشاء رمز تحقق فريد
  static String _generateVerificationToken() {
    final random = Random();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// تشفير البيانات
  static String _hashData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// إرسال رابط التحقق عبر البريد الإلكتروني
  static Future<String?> sendVerificationLink(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      // إنشاء رمز التحقق
      final token = _generateVerificationToken();
      final hashedToken = _hashData(token);
      final expiresAt = DateTime.now().add(_linkExpiration);

      // حفظ بيانات التحقق في Firestore (بدون كلمة المرور لأسباب أمنية)
      await _firestore.collection(_collectionName).doc(email).set({
        'hashedToken': hashedToken,
        'email': email,
        'displayName': displayName,
        'expiresAt': Timestamp.fromDate(expiresAt),
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'isUsed': false,
        'attempts': 0,
      });

      // إنشاء رابط التحقق
      final verificationLink = _buildVerificationLink(email, token);

      // إرسال البريد الإلكتروني
      final emailSent = await _sendVerificationEmail(
        email,
        displayName,
        verificationLink,
      );

      if (emailSent) {
        AppLogger.success(
          'تم إرسال رابط التحقق بنجاح إلى: $email',
          'EmailLinkVerification',
        );
        return verificationLink;
      } else {
        // عرض الرابط في الكونسول كبديل
        _displayLinkInConsole(email, verificationLink);
        return verificationLink;
      }
    } catch (e) {
      AppLogger.error('خطأ في إرسال رابط التحقق', 'EmailLinkVerification', e);
      return null;
    }
  }

  /// بناء رابط التحقق
  static String _buildVerificationLink(String email, String token) {
    final encodedEmail = Uri.encodeComponent(email);
    return 'https://legal2025.firebaseapp.com/verify?email=$encodedEmail&token=$token';
  }

  /// إرسال البريد الإلكتروني مع رابط التحقق
  static Future<bool> _sendVerificationEmail(
    String email,
    String displayName,
    String verificationLink,
  ) async {
    try {
      final emailContent = _buildEmailContent(displayName, verificationLink);
      return await RealEmailService.sendVerificationEmail(email, emailContent);
    } catch (e) {
      AppLogger.error(
        'خطأ في إرسال البريد الإلكتروني',
        'EmailLinkVerification',
        e,
      );
      return false;
    }
  }

  /// بناء محتوى البريد الإلكتروني
  static String _buildEmailContent(
    String displayName,
    String verificationLink,
  ) {
    return '''
مرحباً $displayName!

مرحباً بك في تطبيق Legal 2025!

لإكمال عملية إنشاء حسابك، يرجى النقر على الرابط التالي:

$verificationLink

أو انسخ الرابط والصقه في متصفحك.

ملاحظات مهمة:
• هذا الرابط صالح لمدة 30 دقيقة فقط
• لا تشارك هذا الرابط مع أي شخص آخر
• إذا لم تطلب إنشاء حساب، يرجى تجاهل هذه الرسالة

بعد النقر على الرابط، سيتم تفعيل حسابك تلقائياً وستتمكن من تسجيل الدخول.

شكراً لانضمامك إلى تطبيق Legal 2025!

فريق Legal 2025
    ''';
  }

  /// عرض الرابط في الكونسول
  static void _displayLinkInConsole(String email, String verificationLink) {
    AppLogger.displayVerificationLink(email, verificationLink);
  }

  /// التحقق من صحة رابط التحقق وإنشاء الحساب
  static Future<Map<String, dynamic>> verifyLinkAndCreateAccount(
    String email,
    String token,
  ) async {
    try {
      final docRef = _firestore.collection(_collectionName).doc(email);
      final doc = await docRef.get();

      if (!doc.exists) {
        return {
          'success': false,
          'message': 'رابط التحقق غير صالح أو منتهي الصلاحية.',
        };
      }

      final data = doc.data()!;
      final hashedToken = data['hashedToken'] as String;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final isUsed = data['isUsed'] as bool? ?? false;
      final displayName = data['displayName'] as String;

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().isAfter(expiresAt)) {
        await docRef.delete();
        return {
          'success': false,
          'message': 'انتهت صلاحية رابط التحقق. يرجى طلب رابط جديد.',
        };
      }

      // التحقق من استخدام الرابط مسبقاً
      if (isUsed) {
        return {'success': false, 'message': 'تم استخدام هذا الرابط مسبقاً.'};
      }

      // التحقق من صحة الرمز
      final inputHashedToken = _hashData(token);
      if (inputHashedToken != hashedToken) {
        return {'success': false, 'message': 'رابط التحقق غير صالح.'};
      }

      // إنشاء الحساب في Firebase Auth
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: 'temp_password_${DateTime.now().millisecondsSinceEpoch}',
      );

      // تحديث معلومات المستخدم
      await credential.user?.updateDisplayName(displayName);
      // ملاحظة: البريد الإلكتروني تم تعيينه بالفعل عند إنشاء الحساب

      // إنشاء مستند المستخدم في Firestore
      await _firestore.collection('users').doc(credential.user!.uid).set({
        'uid': credential.user!.uid,
        'email': email,
        'displayName': displayName,
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'isEmailVerified': true,
        'loginProvider': 'email',
        'academicYear': 'السنة الأولى',
        'preferences': {},
      });

      // تحديث حالة الاستخدام
      await docRef.update({'isUsed': true});

      return {
        'success': true,
        'message': 'تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.',
        'user': credential.user,
      };
    } catch (e) {
      AppLogger.error('خطأ في التحقق من الرابط', 'EmailLinkVerification', e);
      return {
        'success': false,
        'message': 'حدث خطأ أثناء التحقق من الرابط. يرجى المحاولة مرة أخرى.',
      };
    }
  }

  /// حذف بيانات التحقق
  static Future<void> deleteVerificationData(String email) async {
    try {
      await _firestore.collection(_collectionName).doc(email).delete();
    } catch (e) {
      // تجاهل أخطاء الحذف
    }
  }

  /// التحقق من وجود رابط صالح
  static Future<bool> hasValidLink(String email) async {
    try {
      final doc = await _firestore.collection(_collectionName).doc(email).get();

      if (!doc.exists) return false;

      final data = doc.data()!;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final isUsed = data['isUsed'] as bool? ?? false;

      return !isUsed && DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }
}
