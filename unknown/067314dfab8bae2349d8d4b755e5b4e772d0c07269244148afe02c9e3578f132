import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الإشعار
class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String body;
  final NotificationType type;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final bool isRead;
  final String? imageUrl;
  final String? actionUrl;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    required this.createdAt,
    this.isRead = false,
    this.imageUrl,
    this.actionUrl,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => NotificationType.general,
      ),
      data: Map<String, dynamic>.from(data['data'] ?? {}),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      isRead: data['isRead'] ?? false,
      imageUrl: data['imageUrl'],
      actionUrl: data['actionUrl'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'title': title,
      'body': body,
      'type': type.toString(),
      'data': data,
      'createdAt': Timestamp.fromDate(createdAt),
      'isRead': isRead,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    NotificationType? type,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    String? imageUrl,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  /// إنشاء إشعار للإعجاب
  factory NotificationModel.like({
    required String userId,
    required String likerName,
    required String postId,
    required String postTitle,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'إعجاب جديد',
      body: 'أعجب $likerName بمنشورك: ${_truncateText(postTitle, 50)}',
      type: NotificationType.like,
      data: {
        'postId': postId,
        'likerName': likerName,
      },
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء إشعار للتعليق
  factory NotificationModel.comment({
    required String userId,
    required String commenterName,
    required String postId,
    required String postTitle,
    required String commentText,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'تعليق جديد',
      body: 'علق $commenterName على منشورك: ${_truncateText(commentText, 50)}',
      type: NotificationType.comment,
      data: {
        'postId': postId,
        'commenterName': commenterName,
        'commentText': commentText,
      },
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء إشعار للمشاركة
  factory NotificationModel.share({
    required String userId,
    required String sharerName,
    required String postId,
    required String postTitle,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'مشاركة جديدة',
      body: 'شارك $sharerName منشورك: ${_truncateText(postTitle, 50)}',
      type: NotificationType.share,
      data: {
        'postId': postId,
        'sharerName': sharerName,
      },
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء إشعار لمنشور جديد
  factory NotificationModel.newPost({
    required String userId,
    required String authorName,
    required String postId,
    required String postTitle,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: 'منشور جديد',
      body: 'نشر $authorName منشوراً جديداً: ${_truncateText(postTitle, 50)}',
      type: NotificationType.newPost,
      data: {
        'postId': postId,
        'authorName': authorName,
      },
      createdAt: DateTime.now(),
    );
  }

  /// إنشاء إشعار عام
  factory NotificationModel.general({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: '',
      userId: userId,
      title: title,
      body: body,
      type: NotificationType.general,
      data: data ?? {},
      createdAt: DateTime.now(),
      imageUrl: imageUrl,
      actionUrl: actionUrl,
    );
  }

  /// اختصار النص
  static String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// الحصول على أيقونة الإشعار
  String get iconPath {
    switch (type) {
      case NotificationType.like:
        return '❤️';
      case NotificationType.comment:
        return '💬';
      case NotificationType.share:
        return '🔄';
      case NotificationType.newPost:
        return '📝';
      case NotificationType.general:
        return '🔔';
      case NotificationType.system:
        return '⚙️';
    }
  }

  /// الحصول على لون الإشعار
  String get colorHex {
    switch (type) {
      case NotificationType.like:
        return '#EF4444'; // أحمر
      case NotificationType.comment:
        return '#3B82F6'; // أزرق
      case NotificationType.share:
        return '#10B981'; // أخضر
      case NotificationType.newPost:
        return '#8B5CF6'; // بنفسجي
      case NotificationType.general:
        return '#6B7280'; // رمادي
      case NotificationType.system:
        return '#F59E0B'; // برتقالي
    }
  }

  /// التحقق من انتهاء صلاحية الإشعار
  bool get isExpired {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays > 30; // ينتهي بعد 30 يوم
  }

  /// الحصول على وقت الإشعار بصيغة نسبية
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    }
  }
}

/// أنواع الإشعارات
enum NotificationType {
  like,      // إعجاب
  comment,   // تعليق
  share,     // مشاركة
  newPost,   // منشور جديد
  general,   // عام
  system,    // نظام
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool enableLikes;
  final bool enableComments;
  final bool enableShares;
  final bool enableNewPosts;
  final bool enableGeneral;
  final bool enableSystem;
  final bool enableSound;
  final bool enableVibration;
  final String quietHoursStart;
  final String quietHoursEnd;

  NotificationSettings({
    this.enableLikes = true,
    this.enableComments = true,
    this.enableShares = true,
    this.enableNewPosts = true,
    this.enableGeneral = true,
    this.enableSystem = true,
    this.enableSound = true,
    this.enableVibration = true,
    this.quietHoursStart = '22:00',
    this.quietHoursEnd = '08:00',
  });

  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      enableLikes: map['enableLikes'] ?? true,
      enableComments: map['enableComments'] ?? true,
      enableShares: map['enableShares'] ?? true,
      enableNewPosts: map['enableNewPosts'] ?? true,
      enableGeneral: map['enableGeneral'] ?? true,
      enableSystem: map['enableSystem'] ?? true,
      enableSound: map['enableSound'] ?? true,
      enableVibration: map['enableVibration'] ?? true,
      quietHoursStart: map['quietHoursStart'] ?? '22:00',
      quietHoursEnd: map['quietHoursEnd'] ?? '08:00',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'enableLikes': enableLikes,
      'enableComments': enableComments,
      'enableShares': enableShares,
      'enableNewPosts': enableNewPosts,
      'enableGeneral': enableGeneral,
      'enableSystem': enableSystem,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
    };
  }

  /// التحقق من تمكين نوع إشعار معين
  bool isTypeEnabled(NotificationType type) {
    switch (type) {
      case NotificationType.like:
        return enableLikes;
      case NotificationType.comment:
        return enableComments;
      case NotificationType.share:
        return enableShares;
      case NotificationType.newPost:
        return enableNewPosts;
      case NotificationType.general:
        return enableGeneral;
      case NotificationType.system:
        return enableSystem;
    }
  }

  /// التحقق من وقت الهدوء
  bool get isQuietTime {
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    // تحويل الأوقات إلى دقائق للمقارنة
    final currentMinutes = _timeToMinutes(currentTime);
    final startMinutes = _timeToMinutes(quietHoursStart);
    final endMinutes = _timeToMinutes(quietHoursEnd);
    
    if (startMinutes <= endMinutes) {
      // نفس اليوم
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // عبر منتصف الليل
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }
}
