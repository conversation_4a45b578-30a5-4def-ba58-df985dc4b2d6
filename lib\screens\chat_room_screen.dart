import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../theme/app_theme.dart';
import '../providers/theme_provider.dart';
import '../models/chat_model.dart';
import '../services/chat_service.dart';
import 'dart:async';

class ChatRoomScreen extends StatefulWidget {
  final ChatRoomModel chatRoom;

  const ChatRoomScreen({super.key, required this.chatRoom});

  @override
  State<ChatRoomScreen> createState() => _ChatRoomScreenState();
}

class _ChatRoomScreenState extends State<ChatRoomScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  bool _isUserMember = false;
  bool _isJoining = false;
  bool _isTyping = false;
  Timer? _typingTimer;
  Timer? _cleanupTimer;

  List<MessageModel> _messages = [];
  List<TypingIndicator> _typingIndicators = [];
  List<ChatUser> _onlineUsers = [];

  @override
  void initState() {
    super.initState();
    _initializeChat();
    _setupTypingListener();
    _startCleanupTimer();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _typingTimer?.cancel();
    _cleanupTimer?.cancel();
    super.dispose();
  }

  void _initializeChat() async {
    // التحقق من عضوية المستخدم
    if (!widget.chatRoom.isGeneral) {
      _isUserMember = await ChatService.isUserMember(widget.chatRoom.id);
      setState(() {});
    } else {
      _isUserMember = true;
    }

    // تحديث حالة الحضور
    await ChatService.updatePresence(true);

    // الاستماع للرسائل إذا كان المستخدم عضواً
    if (_isUserMember || widget.chatRoom.isGeneral) {
      _listenToMessages();
      _listenToTypingIndicators();
      _listenToOnlineUsers();
    }
  }

  void _listenToMessages() {
    ChatService.getMessagesStream(widget.chatRoom.id).listen((messages) {
      setState(() {
        _messages = messages;
      });
      _scrollToBottom();
    });
  }

  void _listenToTypingIndicators() {
    ChatService.getTypingIndicatorsStream(widget.chatRoom.id).listen((
      indicators,
    ) {
      setState(() {
        _typingIndicators = indicators;
      });
    });
  }

  void _listenToOnlineUsers() {
    ChatService.getOnlineUsersStream().listen((users) {
      setState(() {
        _onlineUsers = users;
      });
    });
  }

  void _setupTypingListener() {
    _messageController.addListener(() {
      if (_messageController.text.isNotEmpty && !_isTyping) {
        _isTyping = true;
        ChatService.sendTypingIndicator(widget.chatRoom.id, true);
      }

      // إعادة تعيين مؤقت الكتابة
      _typingTimer?.cancel();
      _typingTimer = Timer(const Duration(seconds: 2), () {
        if (_isTyping) {
          _isTyping = false;
          ChatService.sendTypingIndicator(widget.chatRoom.id, false);
        }
      });
    });
  }

  void _startCleanupTimer() {
    // تنظيف مؤشرات الكتابة المنتهية الصلاحية كل 10 ثوان
    _cleanupTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      ChatService.cleanupExpiredTypingIndicators();
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _joinChatRoom() async {
    setState(() {
      _isJoining = true;
    });

    final success = await ChatService.joinChatRoom(widget.chatRoom.id);

    if (success) {
      setState(() {
        _isUserMember = true;
        _isJoining = false;
      });

      // بدء الاستماع للرسائل بعد الانضمام
      _listenToMessages();
      _listenToTypingIndicators();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم الانضمام إلى ${widget.chatRoom.name} بنجاح',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      setState(() {
        _isJoining = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في الانضمام إلى الغرفة',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _leaveChatRoom() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'مغادرة الغرفة',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من مغادرة ${widget.chatRoom.name}؟',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'مغادرة',
                  style: GoogleFonts.cairo(color: Colors.red),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ChatService.leaveChatRoom(widget.chatRoom.id);

      if (success && mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم مغادرة ${widget.chatRoom.name}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // إيقاف مؤشر الكتابة
    if (_isTyping) {
      _isTyping = false;
      ChatService.sendTypingIndicator(widget.chatRoom.id, false);
    }

    _messageController.clear();

    final success = await ChatService.sendMessage(
      chatRoomId: widget.chatRoom.id,
      message: message,
    );

    if (!success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في إرسال الرسالة', style: GoogleFonts.cairo()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: Column(
            children: [
              // عرض المستخدمين المتصلين
              if (_onlineUsers.isNotEmpty) _buildOnlineUsersBar(themeProvider),

              // محتوى المحادثة
              Expanded(
                child:
                    _isUserMember || widget.chatRoom.isGeneral
                        ? _buildChatContent(themeProvider)
                        : _buildJoinPrompt(themeProvider),
              ),

              // مؤشرات الكتابة
              if (_typingIndicators.isNotEmpty)
                _buildTypingIndicators(themeProvider),

              // شريط إدخال الرسالة
              if (_isUserMember || widget.chatRoom.isGeneral)
                _buildMessageInput(themeProvider),
            ],
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 1,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors:
                    widget.chatRoom.isGeneral
                        ? [const Color(0xFF667EEA), const Color(0xFF764BA2)]
                        : [const Color(0xFF4FACFE), const Color(0xFF00F2FE)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              widget.chatRoom.isGeneral
                  ? Icons.public_rounded
                  : Icons.school_rounded,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatRoom.name,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                  ),
                ),
                if (_onlineUsers.isNotEmpty)
                  Text(
                    '${_onlineUsers.length} متصل',
                    style: GoogleFonts.cairo(fontSize: 12, color: Colors.green),
                  ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        if (!widget.chatRoom.isGeneral && _isUserMember)
          IconButton(
            icon: const Icon(Icons.exit_to_app_rounded, color: Colors.red),
            onPressed: _leaveChatRoom,
          ),
      ],
    );
  }

  Widget _buildOnlineUsersBar(ThemeProvider themeProvider) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.circle, color: Colors.green, size: 12),
          const SizedBox(width: 8),
          Text(
            '${_onlineUsers.length} متصل الآن',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SizedBox(
              height: 32,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _onlineUsers.take(5).length,
                itemBuilder: (context, index) {
                  final user = _onlineUsers[index];
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: CircleAvatar(
                      radius: 16,
                      backgroundColor: AppTheme.primaryColor,
                      child: Text(
                        user.name.isNotEmpty ? user.name[0].toUpperCase() : 'م',
                        style: GoogleFonts.cairo(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatContent(ThemeProvider themeProvider) {
    if (_messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد رسائل بعد',
              style: GoogleFonts.cairo(fontSize: 16, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ المحادثة بإرسال أول رسالة',
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isMe = message.senderId == FirebaseAuth.instance.currentUser?.uid;
        final showSender =
            index == 0 || _messages[index - 1].senderId != message.senderId;

        return _buildMessageBubble(message, isMe, showSender, themeProvider);
      },
    );
  }

  Widget _buildMessageBubble(
    MessageModel message,
    bool isMe,
    bool showSender,
    ThemeProvider themeProvider,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                message.senderName.isNotEmpty
                    ? message.senderName[0].toUpperCase()
                    : 'م',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Column(
              crossAxisAlignment:
                  isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                if (showSender && !isMe)
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 4,
                      left: 12,
                      right: 12,
                    ),
                    child: Text(
                      message.senderName,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient:
                        isMe
                            ? LinearGradient(
                              colors: [
                                AppTheme.primaryColor,
                                AppTheme.secondaryColor,
                              ],
                            )
                            : null,
                    color:
                        isMe
                            ? null
                            : themeProvider.isDarkMode
                            ? const Color(0xFF374151)
                            : Colors.grey[100],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    message.message,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color:
                          isMe
                              ? Colors.white
                              : themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTime(message.timestamp),
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.secondaryColor,
              child: Text(
                message.senderName.isNotEmpty
                    ? message.senderName[0].toUpperCase()
                    : 'أ',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildJoinPrompt(ThemeProvider themeProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                Icons.group_add_rounded,
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'انضم إلى ${widget.chatRoom.name}',
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              widget.chatRoom.description,
              style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _isJoining ? null : _joinChatRoom,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child:
                  _isJoining
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : Text(
                        'انضمام',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypingIndicators(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF374151)
                      : Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ...List.generate(3, (index) {
                  return Container(
                    margin: EdgeInsets.only(right: index < 2 ? 4 : 0),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                  );
                }),
                const SizedBox(width: 8),
                Text(
                  _typingIndicators.length == 1
                      ? '${_typingIndicators.first.userName} يكتب...'
                      : '${_typingIndicators.length} أشخاص يكتبون...',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.withValues(alpha: 0.2), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color:
                    themeProvider.isDarkMode
                        ? const Color(0xFF374151)
                        : Colors.grey[100],
                borderRadius: BorderRadius.circular(25),
              ),
              child: TextField(
                controller: _messageController,
                focusNode: _messageFocusNode,
                decoration: InputDecoration(
                  hintText: 'اكتب رسالة...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey[500]),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.cairo(
                  color:
                      themeProvider.isDarkMode ? Colors.white : Colors.black87,
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
              ),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _sendMessage,
              icon: const Icon(Icons.send_rounded, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime? timestamp) {
    if (timestamp == null) return '';

    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}س';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}د';
    } else {
      return 'الآن';
    }
  }
}
