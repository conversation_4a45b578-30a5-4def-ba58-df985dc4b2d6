import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../models/interaction_models.dart';

/// نافذة البحث المتقدم
class SearchDialog extends StatefulWidget {
  final Function(String query, Map<String, dynamic> filters) onSearch;

  const SearchDialog({
    super.key,
    required this.onSearch,
  });

  @override
  State<SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<SearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _selectedCategory = 'الكل';
  PostSortBy _sortBy = PostSortBy.newest;
  final List<String> _selectedTags = [];

  final List<String> _categories = [
    'الكل',
    'أسئلة',
    'مناقشات',
    'مشاركات',
    'إعلانات',
  ];

  final List<String> _availableTags = [
    'قانون',
    'شريعة',
    'فقه',
    'أصول',
    'مدني',
    'جنائي',
    'تجاري',
    'دستوري',
    'إداري',
    'دولي',
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
            decoration: BoxDecoration(
              color: themeProvider.isDarkMode
                  ? const Color(0xFF1E293B)
                  : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(themeProvider),
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildSearchField(themeProvider),
                        const SizedBox(height: 20),
                        _buildCategoryFilter(themeProvider),
                        const SizedBox(height: 20),
                        _buildSortOptions(themeProvider),
                        const SizedBox(height: 20),
                        _buildTagsFilter(themeProvider),
                      ],
                    ),
                  ),
                ),
                _buildActions(themeProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.search,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'البحث المتقدم',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'كلمات البحث',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: themeProvider.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث في المنشورات...',
            hintStyle: GoogleFonts.cairo(
              color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: themeProvider.isDarkMode
                ? Colors.grey[800]
                : Colors.grey[100],
          ),
          style: GoogleFonts.cairo(
            color: themeProvider.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفئة',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: themeProvider.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _categories.map((category) {
            final isSelected = _selectedCategory == category;
            return FilterChip(
              label: Text(
                category,
                style: GoogleFonts.cairo(
                  color: isSelected ? Colors.white : 
                    (themeProvider.isDarkMode ? Colors.white : Colors.black),
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              selectedColor: const Color(0xFF10B981),
              backgroundColor: themeProvider.isDarkMode
                  ? Colors.grey[800]
                  : Colors.grey[200],
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSortOptions(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ترتيب النتائج',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: themeProvider.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<PostSortBy>(
          value: _sortBy,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: themeProvider.isDarkMode
                ? Colors.grey[800]
                : Colors.grey[100],
          ),
          items: PostSortBy.values.map((sortBy) {
            return DropdownMenuItem(
              value: sortBy,
              child: Text(
                _getSortByName(sortBy),
                style: GoogleFonts.cairo(
                  color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _sortBy = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildTagsFilter(ThemeProvider themeProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العلامات',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: themeProvider.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _availableTags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return FilterChip(
              label: Text(
                tag,
                style: GoogleFonts.cairo(
                  color: isSelected ? Colors.white : 
                    (themeProvider.isDarkMode ? Colors.white : Colors.black),
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedTags.add(tag);
                  } else {
                    _selectedTags.remove(tag);
                  }
                });
              },
              selectedColor: const Color(0xFF10B981),
              backgroundColor: themeProvider.isDarkMode
                  ? Colors.grey[800]
                  : Colors.grey[200],
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildActions(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _searchController.clear();
                  _selectedCategory = 'الكل';
                  _sortBy = PostSortBy.newest;
                  _selectedTags.clear();
                });
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'إعادة تعيين',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _performSearch,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'بحث',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _performSearch() {
    final filters = {
      'category': _selectedCategory == 'الكل' ? null : _selectedCategory,
      'tags': _selectedTags.isEmpty ? null : _selectedTags,
      'sortBy': _sortBy,
    };

    widget.onSearch(_searchController.text.trim(), filters);
    Navigator.pop(context);
  }

  String _getSortByName(PostSortBy sortBy) {
    switch (sortBy) {
      case PostSortBy.newest:
        return 'الأحدث';
      case PostSortBy.oldest:
        return 'الأقدم';
      case PostSortBy.mostLiked:
        return 'الأكثر إعجاباً';
      case PostSortBy.mostCommented:
        return 'الأكثر تعليقاً';
      case PostSortBy.mostShared:
        return 'الأكثر مشاركة';
    }
  }
}
