import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/post_model.dart';
import '../models/interaction_models.dart';
import '../utils/logger.dart';

/// خدمة البحث والفلترة المتقدمة
class SearchService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String postsCollection = 'posts';
  static const String categoriesCollection = 'post_categories';
  static const String tagsCollection = 'tags';

  /// البحث في المنشورات
  static Future<List<PostModel>> searchPosts({
    String? query,
    String? category,
    List<String>? tags,
    DateTime? startDate,
    DateTime? endDate,
    String? authorId,
    bool? hasImages,
    bool? hasAttachments,
    bool? hasPoll,
    PostSortBy sortBy = PostSortBy.newest,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query queryRef = _firestore.collection(postsCollection);

      // فلترة حسب النص
      if (query != null && query.trim().isNotEmpty) {
        // البحث في المحتوى والعنوان
        queryRef = queryRef.where(
          'searchKeywords',
          arrayContainsAny: _generateSearchKeywords(query),
        );
      }

      // فلترة حسب الفئة
      if (category != null && category != 'الكل') {
        queryRef = queryRef.where('category', isEqualTo: category);
      }

      // فلترة حسب العلامات
      if (tags != null && tags.isNotEmpty) {
        queryRef = queryRef.where('tags', arrayContainsAny: tags);
      }

      // فلترة حسب التاريخ
      if (startDate != null) {
        queryRef = queryRef.where(
          'createdAt',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
        );
      }
      if (endDate != null) {
        queryRef = queryRef.where(
          'createdAt',
          isLessThanOrEqualTo: Timestamp.fromDate(endDate),
        );
      }

      // فلترة حسب المؤلف
      if (authorId != null) {
        queryRef = queryRef.where('authorId', isEqualTo: authorId);
      }

      // فلترة حسب وجود الصور
      if (hasImages == true) {
        queryRef = queryRef.where('hasImages', isEqualTo: true);
      }

      // فلترة حسب وجود المرفقات
      if (hasAttachments == true) {
        queryRef = queryRef.where('hasAttachments', isEqualTo: true);
      }

      // فلترة حسب وجود الاستطلاع
      if (hasPoll == true) {
        queryRef = queryRef.where('hasPoll', isEqualTo: true);
      }

      // ترتيب النتائج
      switch (sortBy) {
        case PostSortBy.newest:
          queryRef = queryRef.orderBy('createdAt', descending: true);
          break;
        case PostSortBy.oldest:
          queryRef = queryRef.orderBy('createdAt', descending: false);
          break;
        case PostSortBy.mostLiked:
          queryRef = queryRef.orderBy('likes', descending: true);
          break;
        case PostSortBy.mostCommented:
          queryRef = queryRef.orderBy('commentsCount', descending: true);
          break;
        case PostSortBy.mostShared:
          queryRef = queryRef.orderBy('shares', descending: true);
          break;
      }

      // تحديد الحد الأقصى
      queryRef = queryRef.limit(limit);

      // التصفح التدريجي
      if (lastDocument != null) {
        queryRef = queryRef.startAfterDocument(lastDocument);
      }

      final snapshot = await queryRef.get();
      return snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
    } catch (e) {
      AppLogger.error('خطأ في البحث', 'SearchService', e);
      return [];
    }
  }

  /// البحث السريع (للاقتراحات)
  static Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.trim().isEmpty) return [];

      final keywords = _generateSearchKeywords(query);
      final suggestions = <String>{};

      // البحث في المنشورات الحديثة
      final postsSnapshot =
          await _firestore
              .collection(postsCollection)
              .where('searchKeywords', arrayContainsAny: keywords)
              .orderBy('createdAt', descending: true)
              .limit(10)
              .get();

      for (final doc in postsSnapshot.docs) {
        final data = doc.data();
        final content = data['content'] as String? ?? '';
        final words = content
            .split(' ')
            .where(
              (word) =>
                  word.length > 2 &&
                  word.toLowerCase().contains(query.toLowerCase()),
            );
        suggestions.addAll(words);
      }

      // البحث في العلامات
      final tagsSnapshot =
          await _firestore
              .collection(tagsCollection)
              .where('name', isGreaterThanOrEqualTo: query)
              .where('name', isLessThan: '${query}z')
              .limit(5)
              .get();

      for (final doc in tagsSnapshot.docs) {
        suggestions.add(doc.data()['name'] as String);
      }

      return suggestions.take(10).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الاقتراحات', 'SearchService', e);
      return [];
    }
  }

  /// الحصول على الفئات المتاحة
  static Future<List<PostCategoryModel>> getCategories() async {
    try {
      final snapshot =
          await _firestore
              .collection(categoriesCollection)
              .orderBy('order')
              .get();

      return snapshot.docs
          .map((doc) => PostCategoryModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الفئات', 'SearchService', e);
      return [];
    }
  }

  /// الحصول على العلامات الشائعة
  static Future<List<String>> getPopularTags({int limit = 20}) async {
    try {
      final snapshot =
          await _firestore
              .collection(tagsCollection)
              .orderBy('usageCount', descending: true)
              .limit(limit)
              .get();

      return snapshot.docs.map((doc) => doc.data()['name'] as String).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على العلامات الشائعة', 'SearchService', e);
      return [];
    }
  }

  /// البحث في المستخدمين
  static Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      if (query.trim().isEmpty) return [];

      final snapshot =
          await _firestore
              .collection('users')
              .where('displayName', isGreaterThanOrEqualTo: query)
              .where('displayName', isLessThan: '${query}z')
              .limit(10)
              .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'name': data['displayName'] ?? 'مستخدم',
          'email': data['email'] ?? '',
          'photoUrl': data['photoUrl'],
        };
      }).toList();
    } catch (e) {
      AppLogger.error('خطأ في البحث عن المستخدمين', 'SearchService', e);
      return [];
    }
  }

  /// حفظ البحث الأخير
  static Future<void> saveRecentSearch(String query) async {
    try {
      final user = _auth.currentUser;
      if (user == null || query.trim().isEmpty) return;

      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('recentSearches')
          .add({
            'query': query.trim(),
            'timestamp': FieldValue.serverTimestamp(),
          });

      // الاحتفاظ بآخر 10 عمليات بحث فقط
      final recentSearches =
          await _firestore
              .collection('users')
              .doc(user.uid)
              .collection('recentSearches')
              .orderBy('timestamp', descending: true)
              .get();

      if (recentSearches.docs.length > 10) {
        for (int i = 10; i < recentSearches.docs.length; i++) {
          await recentSearches.docs[i].reference.delete();
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في حفظ البحث الأخير', 'SearchService', e);
    }
  }

  /// الحصول على عمليات البحث الأخيرة
  static Future<List<String>> getRecentSearches() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot =
          await _firestore
              .collection('users')
              .doc(user.uid)
              .collection('recentSearches')
              .orderBy('timestamp', descending: true)
              .limit(10)
              .get();

      return snapshot.docs.map((doc) => doc.data()['query'] as String).toList();
    } catch (e) {
      AppLogger.error(
        'خطأ في الحصول على عمليات البحث الأخيرة',
        'SearchService',
        e,
      );
      return [];
    }
  }

  /// مسح عمليات البحث الأخيرة
  static Future<void> clearRecentSearches() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final batch = _firestore.batch();
      final snapshot =
          await _firestore
              .collection('users')
              .doc(user.uid)
              .collection('recentSearches')
              .get();

      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      AppLogger.error('خطأ في مسح عمليات البحث الأخيرة', 'SearchService', e);
    }
  }

  /// إنشاء كلمات مفتاحية للبحث
  static List<String> _generateSearchKeywords(String text) {
    final words = text.toLowerCase().split(' ');
    final keywords = <String>{};

    for (final word in words) {
      if (word.length > 2) {
        keywords.add(word);
        // إضافة أجزاء من الكلمة للبحث الجزئي
        for (int i = 3; i <= word.length; i++) {
          keywords.add(word.substring(0, i));
        }
      }
    }

    return keywords.toList();
  }

  /// تحديث إحصائيات العلامة
  static Future<void> updateTagUsage(String tag) async {
    try {
      final tagRef = _firestore.collection(tagsCollection).doc(tag);
      await tagRef.set({
        'name': tag,
        'usageCount': FieldValue.increment(1),
        'lastUsed': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));
    } catch (e) {
      AppLogger.error('خطأ في تحديث إحصائيات العلامة', 'SearchService', e);
    }
  }
}
