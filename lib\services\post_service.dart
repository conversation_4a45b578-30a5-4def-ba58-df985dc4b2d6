import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import '../models/post_model.dart';
import '../models/interaction_models.dart';
import '../utils/logger.dart';
import 'file_storage_service.dart';

/// خدمة إدارة المنشورات والتفاعلات
class PostService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collections
  static const String postsCollection = 'posts';
  static const String likesCollection = 'likes';
  static const String sharesCollection = 'shares';
  static const String notificationsCollection = 'notifications';
  static const String categoriesCollection = 'post_categories';

  /// إنشاء منشور جديد
  static Future<String?> createPost({
    required String content,
    String? authorName,
    List<XFile>? images,
    List<File>? attachments,
    Map<String, dynamic>? pollData,
    bool isAnonymous = false,
    String category = 'عام',
    List<String> tags = const [],
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      AppLogger.info('بدء إنشاء منشور جديد', 'PostService');

      // رفع الصور إذا وجدت
      List<String> imageUrls = [];
      if (images != null && images.isNotEmpty) {
        imageUrls = await _uploadImages(images);
      }

      // رفع المرفقات إذا وجدت
      List<String> attachmentUrls = [];
      if (attachments != null && attachments.isNotEmpty) {
        attachmentUrls = await _uploadAttachments(attachments);
      }

      // إنشاء المنشور
      final postData = {
        'content': content,
        'authorName':
            isAnonymous
                ? 'مجهول'
                : (authorName ?? user.displayName ?? 'مستخدم'),
        'authorId': isAnonymous ? 'anonymous' : user.uid,
        'imageUrls': imageUrls,
        'imageUrl': imageUrls.isNotEmpty ? imageUrls.first : null,
        'attachments': attachmentUrls,
        'pollData': pollData,
        'isAnonymous': isAnonymous,
        'category': category,
        'tags': tags,
        'likes': 0,
        'likedBy': [],
        'comments': [],
        'shares': 0,
        'views': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // التحقق من الاتصال بـ Firebase
      AppLogger.info('محاولة حفظ المنشور في Firestore...', 'PostService');

      final docRef = await _firestore.collection(postsCollection).add(postData);

      // التحقق من نجاح الحفظ
      if (docRef.id.isNotEmpty) {
        AppLogger.success(
          'تم إنشاء المنشور بنجاح: ${docRef.id}',
          'PostService',
        );

        // التحقق من وجود المنشور في قاعدة البيانات
        final savedPost = await docRef.get();
        if (savedPost.exists) {
          AppLogger.success(
            'تم التأكد من حفظ المنشور في قاعدة البيانات',
            'PostService',
          );
          return docRef.id;
        } else {
          AppLogger.error('المنشور لم يُحفظ في قاعدة البيانات', 'PostService');
          return null;
        }
      } else {
        AppLogger.error('فشل في الحصول على معرف المنشور', 'PostService');
        return null;
      }
    } catch (e) {
      AppLogger.error('خطأ في إنشاء المنشور', 'PostService', e);

      // تفاصيل إضافية للخطأ
      if (e.toString().contains('permission-denied')) {
        AppLogger.error(
          'خطأ في الصلاحيات - تحقق من Firebase Rules',
          'PostService',
        );
      } else if (e.toString().contains('network')) {
        AppLogger.error(
          'خطأ في الشبكة - تحقق من الاتصال بالإنترنت',
          'PostService',
        );
      } else if (e.toString().contains('unauthenticated')) {
        AppLogger.error('المستخدم غير مصادق عليه', 'PostService');
      }

      return null;
    }
  }

  /// الحصول على المنشورات (Future للتحميل لمرة واحدة)
  static Future<List<PostModel>> getPosts({
    String? category,
    List<String>? tags,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) async {
    try {
      Query query = _firestore
          .collection(postsCollection)
          .orderBy('createdAt', descending: true);

      if (category != null && category != 'الكل') {
        query = query.where('category', isEqualTo: category);
      }

      if (tags != null && tags.isNotEmpty) {
        query = query.where('tags', arrayContainsAny: tags);
      }

      query = query.limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final querySnapshot = await query.get();

      return querySnapshot.docs.map((doc) {
        return PostModel.fromFirestore(doc);
      }).toList();
    } catch (e) {
      AppLogger.error('خطأ في جلب المنشورات', 'PostService', e);
      return [];
    }
  }

  /// الحصول على المنشورات مع التحديث الفوري
  static Stream<List<PostModel>> getPostsStream({
    String? category,
    List<String>? tags,
    int limit = 20,
    DocumentSnapshot? lastDocument,
  }) {
    try {
      Query query = _firestore
          .collection(postsCollection)
          .orderBy('createdAt', descending: true);

      if (category != null && category != 'الكل') {
        query = query.where('category', isEqualTo: category);
      }

      if (tags != null && tags.isNotEmpty) {
        query = query.where('tags', arrayContainsAny: tags);
      }

      query = query.limit(limit);

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => PostModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      AppLogger.error('خطأ في جلب المنشورات', 'PostService', e);
      return Stream.value([]);
    }
  }

  /// إضافة إعجاب للمنشور
  static Future<bool> toggleLike(String postId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final postRef = _firestore.collection(postsCollection).doc(postId);

      return await _firestore.runTransaction((transaction) async {
        final postDoc = await transaction.get(postRef);
        if (!postDoc.exists) return false;

        final data = postDoc.data()!;
        final likedBy = List<String>.from(data['likedBy'] ?? []);
        final likes = data['likes'] ?? 0;

        if (likedBy.contains(user.uid)) {
          // إزالة الإعجاب
          likedBy.remove(user.uid);
          transaction.update(postRef, {'likedBy': likedBy, 'likes': likes - 1});
        } else {
          // إضافة الإعجاب
          likedBy.add(user.uid);
          transaction.update(postRef, {'likedBy': likedBy, 'likes': likes + 1});

          // إنشاء إشعار للمؤلف
          if (data['authorId'] != user.uid && data['authorId'] != 'anonymous') {
            await _createNotification(
              userId: data['authorId'],
              type: 'like',
              title: 'إعجاب جديد',
              message: '${user.displayName ?? 'مستخدم'} أعجب بمنشورك',
              postId: postId,
              fromUserId: user.uid,
              fromUserName: user.displayName ?? 'مستخدم',
            );
          }
        }

        return true;
      });
    } catch (e) {
      AppLogger.error('خطأ في تبديل الإعجاب', 'PostService', e);
      return false;
    }
  }

  /// إضافة تعليق
  static Future<bool> addComment({
    required String postId,
    required String comment,
    String? parentCommentId,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final commentData = CommentModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        comment: comment,
        authorName: user.displayName ?? 'مستخدم',
        authorId: user.uid,
        parentCommentId: parentCommentId,
        createdAt: DateTime.now(),
      );

      final postRef = _firestore.collection(postsCollection).doc(postId);

      await _firestore.runTransaction((transaction) async {
        final postDoc = await transaction.get(postRef);
        if (!postDoc.exists) return;

        final data = postDoc.data()!;
        final comments = List<Map<String, dynamic>>.from(
          data['comments'] ?? [],
        );
        comments.add(commentData.toMap());

        transaction.update(postRef, {'comments': comments});

        // إنشاء إشعار للمؤلف
        if (data['authorId'] != user.uid && data['authorId'] != 'anonymous') {
          await _createNotification(
            userId: data['authorId'],
            type: 'comment',
            title: 'تعليق جديد',
            message: '${user.displayName ?? 'مستخدم'} علق على منشورك',
            postId: postId,
            fromUserId: user.uid,
            fromUserName: user.displayName ?? 'مستخدم',
          );
        }
      });

      AppLogger.success('تم إضافة التعليق بنجاح', 'PostService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في إضافة التعليق', 'PostService', e);
      return false;
    }
  }

  /// مشاركة المنشور
  static Future<bool> sharePost(String postId, {String? message}) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final shareData = ShareModel(
        id: '',
        userId: user.uid,
        userName: user.displayName ?? 'مستخدم',
        postId: postId,
        message: message,
        createdAt: DateTime.now(),
      );

      await _firestore.collection(sharesCollection).add(shareData.toMap());

      // تحديث عداد المشاركات
      final postRef = _firestore.collection(postsCollection).doc(postId);
      await postRef.update({'shares': FieldValue.increment(1)});

      AppLogger.success('تم مشاركة المنشور بنجاح', 'PostService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في مشاركة المنشور', 'PostService', e);
      return false;
    }
  }

  /// رفع الصور
  static Future<List<String>> _uploadImages(List<XFile> images) async {
    List<String> urls = [];

    for (int i = 0; i < images.length; i++) {
      try {
        final file = File(images[i].path);
        final url = await FileStorageService.uploadImage(
          imageFile: file,
          category: 'posts',
          compress: true,
        );

        if (url != null) {
          urls.add(url);
        }
      } catch (e) {
        AppLogger.error('خطأ في رفع الصورة $i', 'PostService', e);
      }
    }

    return urls;
  }

  /// رفع المرفقات
  static Future<List<String>> _uploadAttachments(List<File> files) async {
    List<String> urls = [];

    for (int i = 0; i < files.length; i++) {
      try {
        final file = files[i];
        final url = await FileStorageService.uploadAttachment(file: file);

        if (url != null) {
          urls.add(url);
        }
      } catch (e) {
        AppLogger.error('خطأ في رفع المرفق $i', 'PostService', e);
      }
    }

    return urls;
  }

  /// إنشاء إشعار
  static Future<void> _createNotification({
    required String userId,
    required String type,
    required String title,
    required String message,
    String? postId,
    String? commentId,
    String? fromUserId,
    String? fromUserName,
  }) async {
    try {
      final notification = NotificationModel(
        id: '',
        userId: userId,
        type: type,
        title: title,
        message: message,
        postId: postId,
        commentId: commentId,
        fromUserId: fromUserId,
        fromUserName: fromUserName,
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(notificationsCollection)
          .add(notification.toMap());
    } catch (e) {
      AppLogger.error('خطأ في إنشاء الإشعار', 'PostService', e);
    }
  }

  /// حذف منشور
  static Future<bool> deletePost(String postId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final postDoc =
          await _firestore.collection(postsCollection).doc(postId).get();
      if (!postDoc.exists) return false;

      final data = postDoc.data()!;

      // التحقق من الصلاحية
      if (data['authorId'] != user.uid && !await _isAdmin(user.uid)) {
        return false;
      }

      await _firestore.collection(postsCollection).doc(postId).delete();

      AppLogger.success('تم حذف المنشور بنجاح', 'PostService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف المنشور', 'PostService', e);
      return false;
    }
  }

  /// تحديث عدد المشاهدات
  static Future<void> incrementViews(String postId) async {
    try {
      await _firestore.collection(postsCollection).doc(postId).update({
        'views': FieldValue.increment(1),
      });
    } catch (e) {
      AppLogger.error('خطأ في تحديث المشاهدات', 'PostService', e);
    }
  }

  /// البحث في المنشورات
  static Future<List<PostModel>> searchPosts(String query) async {
    try {
      final snapshot =
          await _firestore
              .collection(postsCollection)
              .where('content', isGreaterThanOrEqualTo: query)
              .where('content', isLessThanOrEqualTo: '$query\uf8ff')
              .orderBy('content')
              .orderBy('createdAt', descending: true)
              .limit(20)
              .get();

      return snapshot.docs.map((doc) => PostModel.fromFirestore(doc)).toList();
    } catch (e) {
      AppLogger.error('خطأ في البحث', 'PostService', e);
      return [];
    }
  }

  /// الحصول على الفئات
  static Future<List<PostCategoryModel>> getCategories() async {
    try {
      final snapshot =
          await _firestore
              .collection(categoriesCollection)
              .where('isActive', isEqualTo: true)
              .get();

      if (snapshot.docs.isEmpty) {
        // إنشاء الفئات الافتراضية
        await _initializeDefaultCategories();
        return PostCategoryModel.getDefaultCategories();
      }

      return snapshot.docs
          .map((doc) => PostCategoryModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      AppLogger.error('خطأ في جلب الفئات', 'PostService', e);
      return PostCategoryModel.getDefaultCategories();
    }
  }

  /// تهيئة الفئات الافتراضية
  static Future<void> _initializeDefaultCategories() async {
    try {
      final categories = PostCategoryModel.getDefaultCategories();
      final batch = _firestore.batch();

      for (final category in categories) {
        final docRef = _firestore
            .collection(categoriesCollection)
            .doc(category.id);
        batch.set(docRef, category.toMap());
      }

      await batch.commit();
      AppLogger.success('تم تهيئة الفئات الافتراضية', 'PostService');
    } catch (e) {
      AppLogger.error('خطأ في تهيئة الفئات', 'PostService', e);
    }
  }

  /// التحقق من صلاحيات الأدمن
  static Future<bool> _isAdmin(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final data = userDoc.data()!;
      return data['isAdmin'] == true;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على الإشعارات
  static Stream<List<NotificationModel>> getNotificationsStream() {
    try {
      final user = _auth.currentUser;
      if (user == null) return Stream.value([]);

      return _firestore
          .collection(notificationsCollection)
          .where('userId', isEqualTo: user.uid)
          .orderBy('createdAt', descending: true)
          .limit(50)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs
                .map((doc) => NotificationModel.fromFirestore(doc))
                .toList();
          });
    } catch (e) {
      AppLogger.error('خطأ في جلب الإشعارات', 'PostService', e);
      return Stream.value([]);
    }
  }

  /// تحديد الإشعار كمقروء
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      AppLogger.error('خطأ في تحديث الإشعار', 'PostService', e);
    }
  }
}
