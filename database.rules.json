{
  "rules": {
    // قواعد المحادثات
    "chats": {
      "$chatRoomId": {
        // قراءة المحادثة متاحة للأعضاء المنضمين فقط
        ".read": "auth != null && (root.child('chatRooms').child($chatRoomId).child('members').child(auth.uid).exists() || root.child('chatRooms').child($chatRoomId).child('isGeneral').val() == true)",
        
        // الكتابة متاحة للأعضاء المنضمين فقط
        ".write": "auth != null && (root.child('chatRooms').child($chatRoomId).child('members').child(auth.uid).exists() || root.child('chatRooms').child($chatRoomId).child('isGeneral').val() == true)",
        
        "messages": {
          "$messageId": {
            // التحقق من صحة بيانات الرسالة
            ".validate": "newData.hasChildren(['senderId', 'senderName', 'message', 'timestamp']) && newData.child('senderId').val() == auth.uid"
          }
        }
      }
    },
    
    // قواعد غرف المحادثة
    "chatRooms": {
      "$chatRoomId": {
        ".read": "auth != null",
        ".write": "auth != null",
        
        "members": {
          "$userId": {
            // المستخدم يمكنه إضافة أو إزالة نفسه فقط
            ".write": "auth != null && $userId == auth.uid"
          }
        }
      }
    },
    
    // قواعد حضور المستخدمين
    "presence": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && $userId == auth.uid"
      }
    },
    
    // قواعد مؤشرات الكتابة
    "typing": {
      "$chatRoomId": {
        ".read": "auth != null && (root.child('chatRooms').child($chatRoomId).child('members').child(auth.uid).exists() || root.child('chatRooms').child($chatRoomId).child('isGeneral').val() == true)",
        
        "$userId": {
          ".write": "auth != null && $userId == auth.uid"
        }
      }
    }
  }
}
